# Character Service

The Character Service manages character creation, deletion, and retrieval in the MMORPG server architecture.

## Overview

The Character Service provides gRPC endpoints for:
- Retrieving character lists for users
- Creating new characters
- Deleting characters
- Retrieving detailed character information

It communicates with the database service to store and retrieve character data.

## Architecture

The service is built using the following components:

- **gRPC Server**: Exposes character management endpoints
- **Character DB Client**: Communicates with the database service for character data

## Service Endpoints

The Character Service exposes the following gRPC endpoints:

### GetCharacterList
Retrieves a list of characters for a user.

```protobuf
rpc GetCharacterList(GetCharacterListRequest) returns (GetCharacterListResponse);
```

### CreateCharacter
Creates a new character for a user.

```protobuf
rpc CreateCharacter(CreateCharacterRequest) returns (CreateCharacterResponse);
```

### DeleteCharacter
Marks a character for deletion or permanently deletes it.

```protobuf
rpc DeleteCharacter(DeleteCharacterRequest) returns (DeleteCharacterResponse);
```

### GetCharacter
Retrieves detailed information about a specific character.

```protobuf
rpc GetCharacter(GetCharacterRequest) returns (GetCharacterResponse);
```

## Character Data Structure

Characters in the system have the following key attributes:

- **Basic Information**: ID, name, user ID, creation/deletion dates
- **Appearance**: Race, face, hair, stone
- **Stats**: Level, attributes (STR, DEX, INT, etc.), HP, MP, experience
- **Inventory**: Items, equipment
- **Position**: Map ID, coordinates

## Configuration

The service can be configured using environment variables:

- `LISTEN_ADDR`: The address to listen on (default: "0.0.0.0")
- `SERVICE_PORT`: The port to listen on (default: "50053")
- `LOG_LEVEL`: Logging level (default: "info")

## Running the Service

### Local Development

```bash
cargo run
```

### Docker

```bash
docker build -t character-service .
docker run -p 50053:50053 character-service
```

## Integration with External Systems

The Character Service integrates with:

- **Database Service**: For character data storage and retrieval
- **Auth Service**: For user authentication and authorization
- **Packet Service**: For handling client requests related to characters
