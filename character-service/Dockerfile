FROM rust:alpine AS builder
LABEL authors="raven"

RUN apk add --no-cache musl-dev libressl-dev protobuf-dev

WORKDIR /usr/src/utils
COPY ./utils .

WORKDIR /usr/src/proto
COPY ./proto .

WORKDIR /usr/src/character-service
COPY ./character-service .

RUN cargo build --release

FROM alpine:3

RUN apk add --no-cache libssl3 libgcc

COPY --from=builder /usr/src/character-service/target/release/character-service /usr/local/bin/character-service

EXPOSE 50054

CMD ["character-service"]