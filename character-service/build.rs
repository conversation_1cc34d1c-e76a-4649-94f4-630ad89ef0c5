fn main() {
    // gRPC Server code
    tonic_build::configure()
        .build_server(true) // Generate gRPC server code
        .compile_well_known_types(true)
        .type_attribute(".", "#[derive(serde::Serialize, serde::Deserialize)]")
        .compile_protos(
            &["../proto/character_common.proto", "../proto/character.proto"],
            &["../proto"],
        )
        .unwrap_or_else(|e| panic!("Failed to compile protos {:?}", e));

    // gRPC Client code
    tonic_build::configure()
        .build_server(false) // Generate gRPC client code
        .compile_well_known_types(true)
        .compile_protos(
            &["../proto/character_db_api.proto", "../proto/auth.proto"],
            &["../proto"],
        )
        .unwrap_or_else(|e| panic!("Failed to compile protos {:?}", e));
}
