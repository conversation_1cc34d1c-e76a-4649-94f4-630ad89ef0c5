[package]
name = "character-service"
version = "0.1.0"
edition = "2021"

[dependencies]
utils = { path = "../utils" }
dotenv = "0.15"
tokio = { version = "1.41.1", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
tracing = "0.1"
tracing-subscriber = { version = "0.3.19", features = ["env-filter", "chrono"] }
tonic = "0.12.3"
prost = "0.13.4"
async-trait = "0.1.83"
serde_json = "1.0.133"
tonic-health = "0.12.3"

[build-dependencies]
tonic-build = "0.12.3"