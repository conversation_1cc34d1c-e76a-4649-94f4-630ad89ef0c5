use crate::character_db_client::CharacterDbClient;
use crate::character_service::character::character_service_server::CharacterService;
use crate::character_service::character::{
    CreateCharacterRequest, CreateCharacterResponse, DeleteCharacterRequest, DeleteCharacterResponse,
    GetCharacterListRequest, GetCharacterListResponse, GetCharacterRequest, GetCharacterResponse,
};
use crate::character_service::character_common::{Character, CharacterFull};
use std::sync::Arc;
use tonic::{Request, Response, Status};
use tracing::debug;

pub mod character_common {
    tonic::include_proto!("character_common");
}
pub mod character {
    tonic::include_proto!("character");
}

pub struct MyCharacterService {
    pub character_db_client: Arc<CharacterDbClient>,
}

#[tonic::async_trait]
impl CharacterService for MyCharacterService {
    async fn get_character_list(
        &self,
        request: Request<GetCharacterListRequest>,
    ) -> Result<Response<GetCharacterListResponse>, Status> {
        let req = request.into_inner();

        let user_id = req.user_id;
        debug!("Character list for User ID: {}", user_id);

        let character_list = self
            .character_db_client
            .as_ref()
            .clone()
            .get_character_list(&user_id)
            .await
            .map_err(|_| Status::aborted("Unable to get character list"))?;
        debug!("{:?}", character_list.characters);

        let mut characters: Vec<Character> = vec![];
        for character in character_list.characters {
            let mut equipped_items: Vec<character_common::EquippedItem> = vec![];
            let inventory: Vec<character_common::Item> = serde_json::from_str(&character.inventory).unwrap();
            for item in inventory {
                if item.slot < 12 {
                    equipped_items.push(character_common::EquippedItem {
                        item_id: item.header.unwrap().id,
                        socket: item.socket,
                        grade: item.grade,
                        gem_option: item.gem_option,
                        slot: item.slot,
                    });
                }
            }
            characters.push(Character {
                character_id: character.id.to_string(),
                name: character.name,
                last_played: 0,
                delete_time: character.deleted_at.parse().unwrap_or_default(),
                stats: serde_json::from_str(&character.stats).unwrap(),
                looks: serde_json::from_str(&character.looks).unwrap(),
                items: equipped_items,
            })
        }

        let response = GetCharacterListResponse { characters };
        Ok(Response::new(response))
    }

    async fn create_character(
        &self,
        request: Request<CreateCharacterRequest>,
    ) -> Result<Response<CreateCharacterResponse>, Status> {
        let req = request.into_inner();
        debug!("{:?}", req);

        let create_character_response = self
            .character_db_client
            .as_ref()
            .clone()
            .create_character(&req.user_id, &req.name, req.race, req.face, req.hair, req.stone)
            .await
            .map_err(|_| Status::aborted("Unable to create character"))?;

        let response = CreateCharacterResponse {
            result: create_character_response.result,
        };
        Ok(Response::new(response))
    }

    async fn delete_character(
        &self,
        request: Request<DeleteCharacterRequest>,
    ) -> Result<Response<DeleteCharacterResponse>, Status> {
        let req = request.into_inner();
        debug!("{:?}", req);

        let delete_character_response = self
            .character_db_client
            .as_ref()
            .clone()
            .delete_character(&req.user_id, &req.char_id, req.delete_type)
            .await
            .map_err(|_| Status::not_found("Character not found"))?;
        let response = DeleteCharacterResponse {
            remaining_time: delete_character_response.remaining_time,
            name: delete_character_response.name,
        };
        Ok(Response::new(response))
    }

    async fn get_character(
        &self,
        request: Request<GetCharacterRequest>,
    ) -> Result<Response<GetCharacterResponse>, Status> {
        let req = request.into_inner();
        debug!("{:?}", req);
        let get_character_response = self
            .character_db_client
            .as_ref()
            .clone()
            .get_character(&req.user_id, &req.char_id)
            .await
            .map_err(|_| Status::not_found("Character not found"))?;

        let character = CharacterFull {
            character_id: get_character_response.id.to_string(),
            name: get_character_response.name.to_string(),
            money: get_character_response.money,
            position: serde_json::from_str(&get_character_response.position).unwrap(),
            looks: serde_json::from_str(&get_character_response.looks).unwrap(),
            stats: serde_json::from_str(&get_character_response.stats).unwrap(),
            skills: serde_json::from_str(&get_character_response.skills).unwrap(),
            items: serde_json::from_str(&get_character_response.inventory).unwrap(),
        };

        let response = GetCharacterResponse {
            character: Some(character),
        };
        Ok(Response::new(response))
    }
}
