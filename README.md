# MMORPG Server Architecture

A microservice-based server architecture for an MMORPG game, built with Rust.

## Overview

This project implements a complete server infrastructure for an MMORPG game, using a microservice architecture for scalability and maintainability. Each service is responsible for a specific domain of the game, communicating with other services via gRPC.

## Architecture

The server architecture consists of the following microservices:

### Core Services

- **Auth Service**: Handles user authentication, session validation, and account management
- **Character Service**: Manages character creation, deletion, and retrieval
- **Database Service**: Provides centralized database access for all services
- **Packet Service**: Handles game client communication via custom binary packets
- **World Service**: Manages game world state and character interactions

### Support Components

- **Utils**: Shared utilities used by all services
- **Launcher**: Client-side launcher for the game

## Communication Flow

1. **Launcher → Client**: User launches the game via the launcher
2. **Client → Packet Service**: Game client connects to the packet service
3. **Packet Service → Auth Service**: Validates user session
4. **Packet Service → Character Service**: Retrieves character data
5. **Packet Service → World Service**: Manages game world interactions

## Technologies

- **Language**: Rust
- **Communication**: gRPC, custom binary protocol
- **Database**: PostgreSQL (managed by external system)
- **Caching**: Redis
- **Service Discovery**: Kubernetes DNS, Consul
- **Metrics**: Prometheus

## External Dependencies

- **Database Schema**: Managed by an external web application using better-auth
- **User Management**: Handled by the external web application
- **Session Creation**: Initial sessions created by the external web application

## Getting Started

### Prerequisites

- Rust (latest stable)
- PostgreSQL
- Redis
- Protobuf compiler

### Building

```bash
cargo build --release
```

### Running

Each service can be run individually:

```bash
# Auth Service
cd auth-service && cargo run

# Character Service
cd character-service && cargo run

# Database Service
cd database-service && cargo run

# Packet Service
cd packet-service && cargo run

# World Service
cd world-service && cargo run
```

### Docker

Each service includes a Dockerfile for containerized deployment.

## Documentation

Each service includes its own README.md with detailed documentation:

- [Auth Service](auth-service/README.md)
- [Character Service](character-service/README.md)
- [Database Service](database-service/README.md)
- [Packet Service](packet-service/README.md)
- [World Service](world-service/README.md)
- [Utils](utils/README.md)
- [Launcher](launcher/README.md)

## Testing

Run tests for all services:

```bash
cargo test --all
```

## License

This project is licensed under the Apache License 2.0 - see the [LICENSE](LICENSE) file for details.
