/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::<PERSON>;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[derive(Debug)]
pub struct CliCreateCharReq {
    pub(crate) race: u8,
    pub(crate) stone: u8,
    pub(crate) hair: u8,
    pub(crate) face: u8,
    pub(crate) weapon: u8,
    pub(crate) zone: u16,
    pub(crate) name: NullTerminatedString,
}

impl PacketPayload for CliCreateCharReq {}

impl Encode for CliCreateCharReq {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.race.encode(encoder)?;
        self.stone.encode(encoder)?;
        self.hair.encode(encoder)?;
        self.face.encode(encoder)?;
        self.weapon.encode(encoder)?;
        self.zone.encode(encoder)?;
        self.name.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for CliCreateCharReq {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let race = u8::decode(decoder)?;
        let stone = u8::decode(decoder)?;
        let hair = u8::decode(decoder)?;
        let face = u8::decode(decoder)?;
        let weapon = u8::decode(decoder)?;
        let zone = u16::decode(decoder)?;
        let name = NullTerminatedString::decode(decoder)?;
        Ok(Self {
            race,
            stone,
            hair,
            face,
            weapon,
            zone,
            name,
        })
    }
}
