/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::Reader;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[repr(u8)]
#[derive(Debug, Clone)]
pub(crate) enum MoveMode {
    Walk = 0,
    Run = 1,
    Drive = 2,
    Rideon = 3,
}

impl Encode for MoveMode {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        encoder.writer().write(&[self.clone() as u8]).map_err(Into::into)
    }
}

impl<Context> Decode<Context> for MoveMode {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let value = u8::decode(decoder)?;
        match value {
            0 => Ok(MoveMode::Walk),
            1 => Ok(MoveMode::Run),
            2 => Ok(MoveMode::Drive),
            3 => Ok(MoveMode::Rideon),
            _ => Err(bincode::error::DecodeError::OtherString(format!(
                "Invalid value for MoveMode: {}",
                value
            ))),
        }
    }
}

#[derive(Debug)]
pub struct SrvMove {
    pub(crate) id: u16,
    pub(crate) target_id: u16,
    pub(crate) distance: u16,
    pub(crate) x: f32,
    pub(crate) y: f32,
    pub(crate) z: u16,
    pub(crate) move_mode: MoveMode,
}

impl PacketPayload for SrvMove {}

impl Encode for SrvMove {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.id.encode(encoder)?;
        self.target_id.encode(encoder)?;
        self.distance.encode(encoder)?;
        self.x.encode(encoder)?;
        self.y.encode(encoder)?;
        self.z.encode(encoder)?;
        self.move_mode.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for SrvMove {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let id = u16::decode(decoder)?;
        let target_id = u16::decode(decoder)?;
        let distance = u16::decode(decoder)?;
        let x = f32::decode(decoder)?;
        let y = f32::decode(decoder)?;
        let z = u16::decode(decoder)?;
        let move_mode = MoveMode::decode(decoder)?;
        Ok(Self {
            id,
            target_id,
            distance,
            x,
            y,
            z,
            move_mode,
        })
    }
}
