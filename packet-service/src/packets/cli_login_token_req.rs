/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::<PERSON>;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[derive(Debug)]
pub struct CliLoginTokenReq {
    pub(crate) token: NullTerminatedString,
}

impl PacketPayload for CliLoginTokenReq {}

impl Encode for CliLoginTokenReq {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.token.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for CliLoginTokenReq {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let token = NullTerminatedString::decode(decoder)?;
        Ok(Self { token })
    }
}
