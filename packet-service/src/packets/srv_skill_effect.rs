/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::Reader;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[derive(Debug)]
pub struct SrvSkillEffect {
    pub(crate) target: u16,
    pub(crate) source: u16,
    pub(crate) id: u16,
    pub(crate) amount: f32,
    pub(crate) state: u8,
}

impl PacketPayload for SrvSkillEffect {}

impl Encode for SrvSkillEffect {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.target.encode(encoder)?;
        self.source.encode(encoder)?;
        self.id.encode(encoder)?;
        self.amount.encode(encoder)?;
        self.state.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for SrvSkillEffect {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let target = u16::decode(decoder)?;
        let source = u16::decode(decoder)?;
        let id = u16::decode(decoder)?;
        let amount = f32::decode(decoder)?;
        let state = u8::decode(decoder)?;
        Ok(Self {
            target,
            source,
            id,
            amount,
            state,
        })
    }
}
