/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::<PERSON>;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[derive(Debug)]
pub struct CliPickupItemReq {
    pub(crate) item_id: u16,
}

impl PacketPayload for CliPickupItemReq {}

impl Encode for CliPickupItemReq {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.item_id.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for CliPickupItemReq {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let item_id = u16::decode(decoder)?;
        Ok(Self { item_id })
    }
}
