/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::Reader;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[repr(u8)]
#[derive(Debug, Clone)]
pub(crate) enum QuestDataRequestType {
    Add = 0,
    Remove = 1,
    Trigger = 2,
}

impl Encode for QuestDataRequestType {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        encoder.writer().write(&[self.clone() as u8]).map_err(Into::into)
    }
}

impl<Context> Decode<Context> for QuestDataRequestType {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let value = u8::decode(decoder)?;
        match value {
            0 => Ok(QuestDataRequestType::Add),
            1 => Ok(QuestDataRequestType::Remove),
            2 => Ok(QuestDataRequestType::Trigger),
            _ => Err(bincode::error::DecodeError::OtherString(format!(
                "Invalid value for QuestDataRequestType: {}",
                value
            ))),
        }
    }
}

#[derive(Debug)]
pub struct CliQuestDataReq {
    pub(crate) type_: QuestDataRequestType,
    pub(crate) slot: u8,
    pub(crate) id: i32,
}

impl PacketPayload for CliQuestDataReq {}

impl Encode for CliQuestDataReq {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.type_.encode(encoder)?;
        self.slot.encode(encoder)?;
        self.id.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for CliQuestDataReq {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let type_ = QuestDataRequestType::decode(decoder)?;
        let slot = u8::decode(decoder)?;
        let id = i32::decode(decoder)?;
        Ok(Self { type_, slot, id })
    }
}
