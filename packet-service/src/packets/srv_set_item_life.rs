/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::<PERSON>;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[derive(Debug)]
pub struct SrvSetItemLife {
    pub(crate) index: u16,
    pub(crate) life: u16,
}

impl PacketPayload for SrvSetItemLife {}

impl Encode for SrvSetItemLife {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.index.encode(encoder)?;
        self.life.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for SrvSetItemLife {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let index = u16::decode(decoder)?;
        let life = u16::decode(decoder)?;
        Ok(Self { index, life })
    }
}
