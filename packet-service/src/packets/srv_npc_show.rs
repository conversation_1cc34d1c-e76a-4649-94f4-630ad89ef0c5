/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::<PERSON>;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[derive(Debug)]
pub struct SrvNpcShow {
    pub(crate) id: u16,
    pub(crate) show: u8,
}

impl PacketPayload for SrvNpcShow {}

impl Encode for SrvNpcShow {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.id.encode(encoder)?;
        self.show.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for SrvNpcShow {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let id = u16::decode(decoder)?;
        let show = u8::decode(decoder)?;
        Ok(Self { id, show })
    }
}
