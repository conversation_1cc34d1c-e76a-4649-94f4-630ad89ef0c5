/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::<PERSON>;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[derive(Debug)]
pub struct SrvGmCommandCode {
    pub(crate) target: u16,
    pub(crate) command: NullTerminatedString,
}

impl PacketPayload for SrvGmCommandCode {}

impl Encode for SrvGmCommandCode {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.target.encode(encoder)?;
        self.command.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for SrvGmCommandCode {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let target = u16::decode(decoder)?;
        let command = NullTerminatedString::decode(decoder)?;
        Ok(Self { target, command })
    }
}
