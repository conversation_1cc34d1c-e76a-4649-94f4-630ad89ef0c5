/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::Reader;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[repr(u8)]
#[derive(Debug, Clone)]
pub(crate) enum BankMoveMode {
    InventoryToBank = 16,
    BankToInventory = 17,
}

impl Encode for BankMoveMode {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        encoder.writer().write(&[self.clone() as u8]).map_err(Into::into)
    }
}

impl<Context> Decode<Context> for BankMoveMode {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let value = u8::decode(decoder)?;
        match value {
            16 => Ok(BankMoveMode::InventoryToBank),
            17 => Ok(BankMoveMode::BankToInventory),
            _ => Err(bincode::error::DecodeError::OtherString(format!(
                "Invalid value for BankMoveMode: {}",
                value
            ))),
        }
    }
}

#[derive(Debug)]
pub struct CliBankMoveMoney {
    pub(crate) mode: BankMoveMode,
    pub(crate) amount: i64,
}

impl PacketPayload for CliBankMoveMoney {}

impl Encode for CliBankMoveMoney {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.mode.encode(encoder)?;
        self.amount.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for CliBankMoveMoney {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let mode = BankMoveMode::decode(decoder)?;
        let amount = i64::decode(decoder)?;
        Ok(Self { mode, amount })
    }
}
