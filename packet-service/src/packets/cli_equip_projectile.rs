/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::<PERSON>;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[derive(Debug, <PERSON><PERSON>, Default)]
pub struct ProjectileTypeAndIndex {
    pub(crate) type_: u8,
    pub(crate) index: u16,
}

impl Encode for ProjectileTypeAndIndex {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.type_.encode(encoder)?;
        self.index.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for ProjectileTypeAndIndex {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let type_ = u8::decode(decoder)?;
        let index = u16::decode(decoder)?;
        Ok(Self { type_, index })
    }
}

#[derive(Debug)]
pub struct CliEquipProjectile {
    pub(crate) projectile: ProjectileTypeAndIndex,
}

impl PacketPayload for CliEquipProjectile {}

impl Encode for CliEquipProjectile {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.projectile.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for CliEquipProjectile {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let projectile = ProjectileTypeAndIndex::decode(decoder)?;
        Ok(Self { projectile })
    }
}
