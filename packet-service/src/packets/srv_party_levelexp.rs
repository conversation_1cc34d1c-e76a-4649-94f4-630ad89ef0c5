/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::<PERSON>;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[derive(Debug, <PERSON><PERSON>, Default)]
pub struct PartyExpData {
    pub(crate) exp: u32,
    pub(crate) level_up: u8,
}

impl Encode for PartyExpData {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.exp.encode(encoder)?;
        self.level_up.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for PartyExpData {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let exp = u32::decode(decoder)?;
        let level_up = u8::decode(decoder)?;
        Ok(Self { exp, level_up })
    }
}

#[derive(Debug)]
pub struct SrvPartyLevelexp {
    pub(crate) level: u8,
    pub(crate) exp_data: PartyExpData,
}

impl PacketPayload for SrvPartyLevelexp {}

impl Encode for SrvPartyLevelexp {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.level.encode(encoder)?;
        self.exp_data.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for SrvPartyLevelexp {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let level = u8::decode(decoder)?;
        let exp_data = PartyExpData::decode(decoder)?;
        Ok(Self { level, exp_data })
    }
}
