/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::Reader;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[derive(Debug)]
pub struct SrvSetAnimation {
    pub(crate) id: u16,
    pub(crate) value: u16,
    pub(crate) object_id: u16,
}

impl PacketPayload for SrvSetAnimation {}

impl Encode for SrvSetAnimation {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.id.encode(encoder)?;
        self.value.encode(encoder)?;
        self.object_id.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for SrvSetAnimation {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let id = u16::decode(decoder)?;
        let value = u16::decode(decoder)?;
        let object_id = u16::decode(decoder)?;
        Ok(Self { id, value, object_id })
    }
}
