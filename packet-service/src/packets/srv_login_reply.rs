/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::Reader;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[repr(u8)]
#[derive(Debug, Clone)]
pub(crate) enum Result {
    Ok = 0,
    Failed = 1,
    UnknownAccount = 2,
    InvalidPassword = 3,
    AlreadyLoggedin = 4,
    RefusedAccount = 5,
    NeedCharge = 6,
    NoRightToConnect = 7,
    TooManyUsers = 8,
    NoName = 9,
    InvalidVersion = 10,
    OutsideRegion = 11,
}

impl Encode for Result {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        encoder.writer().write(&[self.clone() as u8]).map_err(Into::into)
    }
}

impl<Context> Decode<Context> for Result {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let value = u8::decode(decoder)?;
        match value {
            0 => Ok(Result::Ok),
            1 => Ok(Result::Failed),
            2 => Ok(Result::UnknownAccount),
            3 => Ok(Result::InvalidPassword),
            4 => Ok(Result::AlreadyLoggedin),
            5 => Ok(Result::RefusedAccount),
            6 => Ok(Result::NeedCharge),
            7 => Ok(Result::NoRightToConnect),
            8 => Ok(Result::TooManyUsers),
            9 => Ok(Result::NoName),
            10 => Ok(Result::InvalidVersion),
            11 => Ok(Result::OutsideRegion),
            _ => Err(bincode::error::DecodeError::OtherString(format!(
                "Invalid value for Result: {}",
                value
            ))),
        }
    }
}

#[derive(Debug, Clone, Default)]
pub struct ServerInfo {
    pub(crate) test: u8,
    pub(crate) name: NullTerminatedString,
    pub(crate) id: u32,
}

impl Encode for ServerInfo {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.test.encode(encoder)?;
        self.name.encode(encoder)?;
        self.id.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for ServerInfo {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let test = u8::decode(decoder)?;
        let name = NullTerminatedString::decode(decoder)?;
        let id = u32::decode(decoder)?;
        Ok(Self { test, name, id })
    }
}

#[derive(Debug)]
pub struct SrvLoginReply {
    pub(crate) result: Result,
    pub(crate) right: u16,
    pub(crate) type_: u16,
    pub(crate) servers_info: Vec<ServerInfo>,
}

impl PacketPayload for SrvLoginReply {}

impl Encode for SrvLoginReply {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.result.encode(encoder)?;
        self.right.encode(encoder)?;
        self.type_.encode(encoder)?;
        self.servers_info.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for SrvLoginReply {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let result = Result::decode(decoder)?;
        let right = u16::decode(decoder)?;
        let type_ = u16::decode(decoder)?;
        let servers_info = Vec::decode(decoder)?;
        Ok(Self {
            result,
            right,
            type_,
            servers_info,
        })
    }
}
