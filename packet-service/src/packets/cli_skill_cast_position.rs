/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::<PERSON>;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[derive(Debug)]
pub struct CliSkillCastPosition {
    pub(crate) slot: u8,
    pub(crate) x: f32,
    pub(crate) y: f32,
}

impl PacketPayload for CliSkillCastPosition {}

impl Encode for CliSkillCastPosition {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.slot.encode(encoder)?;
        self.x.encode(encoder)?;
        self.y.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for CliSkillCastPosition {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let slot = u8::decode(decoder)?;
        let x = f32::decode(decoder)?;
        let y = f32::decode(decoder)?;
        Ok(Self { slot, x, y })
    }
}
