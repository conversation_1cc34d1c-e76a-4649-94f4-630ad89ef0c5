/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::<PERSON>;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[derive(Debug)]
pub struct SrvLevelup {
    pub(crate) id: u16,
    pub(crate) level: i16,
    pub(crate) exp: i64,
    pub(crate) stat_points: i16,
    pub(crate) skill_points: i16,
}

impl PacketPayload for SrvLevelup {}

impl Encode for SrvLevelup {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.id.encode(encoder)?;
        self.level.encode(encoder)?;
        self.exp.encode(encoder)?;
        self.stat_points.encode(encoder)?;
        self.skill_points.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for SrvLevelup {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let id = u16::decode(decoder)?;
        let level = i16::decode(decoder)?;
        let exp = i64::decode(decoder)?;
        let stat_points = i16::decode(decoder)?;
        let skill_points = i16::decode(decoder)?;
        Ok(Self {
            id,
            level,
            exp,
            stat_points,
            skill_points,
        })
    }
}
