/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::Reader;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[derive(Debug)]
pub struct CliDropItem {
    pub(crate) index: u8,
    pub(crate) quantity: u32,
}

impl PacketPayload for CliDropItem {}

impl Encode for CliDropItem {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.index.encode(encoder)?;
        self.quantity.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for CliDropItem {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let index = u8::decode(decoder)?;
        let quantity = u32::decode(decoder)?;
        Ok(Self { index, quantity })
    }
}
