/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::<PERSON>;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[repr(u8)]
#[derive(Debug, Clone)]
pub(crate) enum CraftResult {
    Successs = 0,
    Fail = 1,
    InvalidCondition = 2,
    MissingItem = 3,
    InvalidItem = 4,
    InvalidSkillLevel = 5,
}

impl Encode for CraftResult {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        encoder.writer().write(&[self.clone() as u8]).map_err(Into::into)
    }
}

impl<Context> Decode<Context> for CraftResult {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let value = u8::decode(decoder)?;
        match value {
            0 => Ok(CraftResult::Successs),
            1 => Ok(CraftResult::Fail),
            2 => Ok(CraftResult::InvalidCondition),
            3 => Ok(CraftResult::MissingItem),
            4 => Ok(CraftResult::InvalidItem),
            5 => Ok(CraftResult::InvalidSkillLevel),
            _ => Err(bincode::error::DecodeError::OtherString(format!(
                "Invalid value for CraftResult: {}",
                value
            ))),
        }
    }
}

#[derive(Debug, Clone, Default)]
pub struct Header {
    pub(crate) type_: u8,
    pub(crate) id: u16,
    pub(crate) is_created: u8,
}

impl Encode for Header {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.type_.encode(encoder)?;
        self.id.encode(encoder)?;
        self.is_created.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for Header {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let type_ = u8::decode(decoder)?;
        let id = u16::decode(decoder)?;
        let is_created = u8::decode(decoder)?;
        Ok(Self { type_, id, is_created })
    }
}

#[derive(Debug, Clone, Default)]
pub struct Data {
    pub(crate) gem_opt: u16,
    pub(crate) life: u16,
    pub(crate) durability: u8,
    pub(crate) has_socket: u8,
    pub(crate) is_appraised: u8,
    pub(crate) refine: u8,
    pub(crate) count: u32,
}

impl Encode for Data {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.gem_opt.encode(encoder)?;
        self.life.encode(encoder)?;
        self.durability.encode(encoder)?;
        self.has_socket.encode(encoder)?;
        self.is_appraised.encode(encoder)?;
        self.refine.encode(encoder)?;
        self.count.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for Data {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let gem_opt = u16::decode(decoder)?;
        let life = u16::decode(decoder)?;
        let durability = u8::decode(decoder)?;
        let has_socket = u8::decode(decoder)?;
        let is_appraised = u8::decode(decoder)?;
        let refine = u8::decode(decoder)?;
        let count = u32::decode(decoder)?;
        Ok(Self {
            gem_opt,
            life,
            durability,
            has_socket,
            is_appraised,
            refine,
            count,
        })
    }
}

#[derive(Debug, Clone, Default)]
pub struct Item {
    pub(crate) header: Header,
    pub(crate) data: Data,
}

impl Encode for Item {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.header.encode(encoder)?;
        self.data.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for Item {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let header = Header::decode(decoder)?;
        let data = Data::decode(decoder)?;
        Ok(Self { header, data })
    }
}

#[derive(Debug)]
pub struct SrvCraftReply {
    pub(crate) result: CraftResult,
    pub(crate) step_or_index: i16,
    pub(crate) progress: [i16; 4],
    pub(crate) item: Item,
}

impl PacketPayload for SrvCraftReply {}

impl Encode for SrvCraftReply {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.result.encode(encoder)?;
        self.step_or_index.encode(encoder)?;
        for value in &self.progress {
            value.encode(encoder)?;
        }
        self.item.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for SrvCraftReply {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let result = CraftResult::decode(decoder)?;
        let step_or_index = i16::decode(decoder)?;
        let mut progress = [0i16; 4];
        for value in &mut progress {
            *value = i16::decode(decoder)?;
        }
        let item = Item::decode(decoder)?;
        Ok(Self {
            result,
            step_or_index,
            progress,
            item,
        })
    }
}
