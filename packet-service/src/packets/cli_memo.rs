/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::Reader;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[repr(u8)]
#[derive(Debug, Clone)]
pub(crate) enum MemoType {
    RequestContent = 0,
    Send = 1,
    ReceivedCount = 2,
}

impl Encode for MemoType {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        encoder.writer().write(&[self.clone() as u8]).map_err(Into::into)
    }
}

impl<Context> Decode<Context> for MemoType {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let value = u8::decode(decoder)?;
        match value {
            0 => Ok(MemoType::RequestContent),
            1 => Ok(MemoType::Send),
            2 => Ok(MemoType::ReceivedCount),
            _ => Err(bincode::error::DecodeError::OtherString(format!(
                "Invalid value for MemoType: {}",
                value
            ))),
        }
    }
}

#[derive(Debug)]
pub struct CliMemo {
    pub(crate) type_: MemoType,
    pub(crate) target: NullTerminatedString,
    pub(crate) message: NullTerminatedString,
}

impl PacketPayload for CliMemo {}

impl Encode for CliMemo {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.type_.encode(encoder)?;
        self.target.encode(encoder)?;
        self.message.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for CliMemo {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let type_ = MemoType::decode(decoder)?;
        let target = NullTerminatedString::decode(decoder)?;
        let message = NullTerminatedString::decode(decoder)?;
        Ok(Self { type_, target, message })
    }
}
