/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::Reader;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[repr(u8)]
#[derive(Debug, Clone)]
pub(crate) enum QuestDataResult {
    AddSuccess = 0,
    AddFail = 1,
    RemoveSuccess = 2,
    RemoveFail = 3,
    TriggerSuccess = 4,
    TriggerFail = 5,
}

impl Encode for QuestDataResult {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        encoder.writer().write(&[self.clone() as u8]).map_err(Into::into)
    }
}

impl<Context> Decode<Context> for QuestDataResult {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let value = u8::decode(decoder)?;
        match value {
            0 => Ok(QuestDataResult::AddSuccess),
            1 => Ok(QuestDataResult::AddFail),
            2 => Ok(QuestDataResult::RemoveSuccess),
            3 => Ok(QuestDataResult::RemoveFail),
            4 => Ok(QuestDataResult::TriggerSuccess),
            5 => Ok(QuestDataResult::TriggerFail),
            _ => Err(bincode::error::DecodeError::OtherString(format!(
                "Invalid value for QuestDataResult: {}",
                value
            ))),
        }
    }
}

#[derive(Debug)]
pub struct SrvQuestDataReply {
    pub(crate) result: QuestDataResult,
    pub(crate) slot: u8,
    pub(crate) id: i32,
}

impl PacketPayload for SrvQuestDataReply {}

impl Encode for SrvQuestDataReply {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.result.encode(encoder)?;
        self.slot.encode(encoder)?;
        self.id.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for SrvQuestDataReply {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let result = QuestDataResult::decode(decoder)?;
        let slot = u8::decode(decoder)?;
        let id = i32::decode(decoder)?;
        Ok(Self { result, slot, id })
    }
}
