/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::Reader;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[derive(Debug)]
pub struct CliMouseCmd {
    pub(crate) target_id: u16,
    pub(crate) x: f32,
    pub(crate) y: f32,
    pub(crate) z: u16,
}

impl PacketPayload for CliMouseCmd {}

impl Encode for CliMouseCmd {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.target_id.encode(encoder)?;
        self.x.encode(encoder)?;
        self.y.encode(encoder)?;
        self.z.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for CliMouseCmd {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let target_id = u16::decode(decoder)?;
        let x = f32::decode(decoder)?;
        let y = f32::decode(decoder)?;
        let z = u16::decode(decoder)?;
        Ok(Self { target_id, x, y, z })
    }
}
