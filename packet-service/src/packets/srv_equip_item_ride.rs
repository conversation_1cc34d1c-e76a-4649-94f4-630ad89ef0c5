/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::Reader;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[repr(u16)]
#[derive(Debug, Clone)]
pub(crate) enum EquippedPositionRide {
    Body = 0,
    Engine = 1,
    Leg = 2,
    Ability = 3,
    Arm = 4,
}

impl Encode for EquippedPositionRide {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        encoder.writer().write(&[self.clone() as u8]).map_err(Into::into)
    }
}

impl<Context> Decode<Context> for EquippedPositionRide {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let value = u16::decode(decoder)?;
        match value {
            0 => Ok(EquippedPositionRide::Body),
            1 => Ok(EquippedPositionRide::Engine),
            2 => Ok(EquippedPositionRide::Leg),
            3 => Ok(EquippedPositionRide::Ability),
            4 => Ok(EquippedPositionRide::Arm),
            _ => Err(bincode::error::DecodeError::OtherString(format!(
                "Invalid value for EquippedPositionRide: {}",
                value
            ))),
        }
    }
}

#[derive(Debug, Clone, Default)]
pub struct EquippedItem {
    pub(crate) id: u16,
    pub(crate) gem_opt: u16,
    pub(crate) socket: i8,
    pub(crate) grade: u8,
}

impl Encode for EquippedItem {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.id.encode(encoder)?;
        self.gem_opt.encode(encoder)?;
        self.socket.encode(encoder)?;
        self.grade.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for EquippedItem {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let id = u16::decode(decoder)?;
        let gem_opt = u16::decode(decoder)?;
        let socket = i8::decode(decoder)?;
        let grade = u8::decode(decoder)?;
        Ok(Self {
            id,
            gem_opt,
            socket,
            grade,
        })
    }
}

#[derive(Debug)]
pub struct SrvEquipItemRide {
    pub(crate) char_id: u16,
    pub(crate) slot: EquippedPositionRide,
    pub(crate) item: EquippedItem,
    pub(crate) move_speed: u16,
}

impl PacketPayload for SrvEquipItemRide {}

impl Encode for SrvEquipItemRide {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.char_id.encode(encoder)?;
        self.slot.encode(encoder)?;
        self.item.encode(encoder)?;
        self.move_speed.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for SrvEquipItemRide {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let char_id = u16::decode(decoder)?;
        let slot = EquippedPositionRide::decode(decoder)?;
        let item = EquippedItem::decode(decoder)?;
        let move_speed = u16::decode(decoder)?;
        Ok(Self {
            char_id,
            slot,
            item,
            move_speed,
        })
    }
}
