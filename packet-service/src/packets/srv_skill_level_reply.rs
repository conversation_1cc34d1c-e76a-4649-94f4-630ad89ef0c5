/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::Reader;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[repr(u8)]
#[derive(Debug, Clone)]
pub(crate) enum SkillLevelResult {
    Succeeded = 0,
    Failed = 1,
    NoPoints = 2,
    NeedStat = 3,
    NeedJob = 4,
    NeedSkill = 5,
    NoMoney = 6,
}

impl Encode for SkillLevelResult {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        encoder.writer().write(&[self.clone() as u8]).map_err(Into::into)
    }
}

impl<Context> Decode<Context> for SkillLevelResult {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let value = u8::decode(decoder)?;
        match value {
            0 => Ok(SkillLevelResult::Succeeded),
            1 => Ok(SkillLevelResult::Failed),
            2 => Ok(SkillLevelResult::NoPoints),
            3 => Ok(SkillLevelResult::NeedStat),
            4 => Ok(SkillLevelResult::NeedJob),
            5 => Ok(SkillLevelResult::NeedSkill),
            6 => Ok(SkillLevelResult::NoMoney),
            _ => Err(bincode::error::DecodeError::OtherString(format!(
                "Invalid value for SkillLevelResult: {}",
                value
            ))),
        }
    }
}

#[derive(Debug)]
pub struct SrvSkillLevelReply {
    pub(crate) result: SkillLevelResult,
    pub(crate) slot: u8,
    pub(crate) id: i16,
    pub(crate) points: i16,
}

impl PacketPayload for SrvSkillLevelReply {}

impl Encode for SrvSkillLevelReply {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.result.encode(encoder)?;
        self.slot.encode(encoder)?;
        self.id.encode(encoder)?;
        self.points.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for SrvSkillLevelReply {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let result = SkillLevelResult::decode(decoder)?;
        let slot = u8::decode(decoder)?;
        let id = i16::decode(decoder)?;
        let points = i16::decode(decoder)?;
        Ok(Self {
            result,
            slot,
            id,
            points,
        })
    }
}
