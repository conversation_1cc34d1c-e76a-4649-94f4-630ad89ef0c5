/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::Reader;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[derive(Debug)]
pub struct SrvRideStateChange {
    pub(crate) enabled: i8,
    pub(crate) cooldown: u32,
    pub(crate) max_hp: i16,
    pub(crate) target: u16,
}

impl PacketPayload for SrvRideStateChange {}

impl Encode for SrvRideStateChange {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.enabled.encode(encoder)?;
        self.cooldown.encode(encoder)?;
        self.max_hp.encode(encoder)?;
        self.target.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for SrvRideStateChange {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let enabled = i8::decode(decoder)?;
        let cooldown = u32::decode(decoder)?;
        let max_hp = i16::decode(decoder)?;
        let target = u16::decode(decoder)?;
        Ok(Self {
            enabled,
            cooldown,
            max_hp,
            target,
        })
    }
}
