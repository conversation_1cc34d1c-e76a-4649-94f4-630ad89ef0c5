/* This file is @generated with IDL v0.2.2 */

use bincode::{Encode, Decode, enc::Encoder, de::Decoder, error::DecodeError};
use bincode::de::read::Reader;
use bincode::enc::write::Writer;
use utils::null_string::NullTerminatedString;
use crate::enums::*;
use crate::types::*;
use crate::dataconsts::*;
use crate::packet::PacketPayload;

#[repr(u8)]
#[derive(Debug, Clone)]
pub(crate) enum Status {
    Connected = 0,
    Disconnected = 1,
    Switching = 2,
    Afk = 3,
    Invisible = 4,
}

impl Encode for Status {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        encoder.writer().write(&[self.clone() as u8]).map_err(Into::into)
    }
}

impl<Context> Decode<Context> for Status {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let value = u8::decode(decoder)?;
        match value {
            0 => Ok(Status::Connected),
            1 => Ok(Status::Disconnected),
            2 => Ok(Status::Switching),
            3 => Ok(Status::Afk),
            4 => Ok(Status::Invisible),
            _ => Err(bincode::error::DecodeError::OtherString(format!("Invalid value for Status: {}", value))),
        }
    }
}


#[derive(Debug)]
pub struct IscClientStatus {
    pub(crate) entity_map_id: u16, 
    pub(crate) status: Status, 
}

impl PacketPayload for IscClientStatus {}

impl Encode for IscClientStatus {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.entity_map_id.encode(encoder)?;
        self.status.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for IscClientStatus {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let entity_map_id = u16::decode(decoder)?;
        let status = Status::decode(decoder)?;
        Ok(Self { entity_map_id, status })
    }
}
