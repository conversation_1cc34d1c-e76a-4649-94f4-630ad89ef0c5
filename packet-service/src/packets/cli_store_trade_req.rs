/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::Reader;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[repr(u8)]
#[derive(Debug, Clone)]
pub(crate) enum StoreTradeResult {
    PriceDiff = 1,
    InvalidNpc = 2,
    TooFar = 3,
    NoMoney = 4,
    InvalidUnion = 5,
    NoPoints = 6,
}

impl Encode for StoreTradeResult {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        encoder.writer().write(&[self.clone() as u8]).map_err(Into::into)
    }
}

impl<Context> Decode<Context> for StoreTradeResult {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let value = u8::decode(decoder)?;
        match value {
            1 => Ok(StoreTradeResult::PriceDiff),
            2 => Ok(StoreTradeResult::InvalidNpc),
            3 => Ok(StoreTradeResult::TooFar),
            4 => Ok(StoreTradeResult::NoMoney),
            5 => Ok(StoreTradeResult::InvalidUnion),
            6 => Ok(StoreTradeResult::NoPoints),
            _ => Err(bincode::error::DecodeError::OtherString(format!(
                "Invalid value for StoreTradeResult: {}",
                value
            ))),
        }
    }
}

#[derive(Debug)]
pub struct CliStoreTradeReq {
    pub(crate) result: StoreTradeResult,
}

impl PacketPayload for CliStoreTradeReq {}

impl Encode for CliStoreTradeReq {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.result.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for CliStoreTradeReq {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let result = StoreTradeResult::decode(decoder)?;
        Ok(Self { result })
    }
}
