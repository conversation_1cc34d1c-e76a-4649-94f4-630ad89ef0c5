/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::<PERSON>;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[derive(Debug)]
pub struct SrvDeleteCharReply {
    pub(crate) remaining_time: u32,
    pub(crate) name: NullTerminatedString,
}

impl PacketPayload for SrvDeleteCharReply {}

impl Encode for SrvDeleteCharReply {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.remaining_time.encode(encoder)?;
        self.name.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for SrvDeleteCharReply {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let remaining_time = u32::decode(decoder)?;
        let name = NullTerminatedString::decode(decoder)?;
        Ok(Self { remaining_time, name })
    }
}
