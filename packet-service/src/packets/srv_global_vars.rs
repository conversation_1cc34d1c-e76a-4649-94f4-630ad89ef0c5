/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::<PERSON>;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[derive(Debug)]
pub struct SrvGlobalVars {
    pub(crate) world_product: i16,
    pub(crate) update_time: i32,
    pub(crate) world_rate: i16,
    pub(crate) town_rate: u8,
    pub(crate) item_rate: [u8; (MAX_SELL_TYPE as usize)],
    pub(crate) flags: u32,
}

impl PacketPayload for SrvGlobalVars {}

impl Encode for SrvGlobalVars {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.world_product.encode(encoder)?;
        self.update_time.encode(encoder)?;
        self.world_rate.encode(encoder)?;
        self.town_rate.encode(encoder)?;
        for value in &self.item_rate {
            value.encode(encoder)?;
        }
        self.flags.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for SrvGlobalVars {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let world_product = i16::decode(decoder)?;
        let update_time = i32::decode(decoder)?;
        let world_rate = i16::decode(decoder)?;
        let town_rate = u8::decode(decoder)?;
        let mut item_rate = [0u8; (MAX_SELL_TYPE as usize)];
        for value in &mut item_rate {
            *value = u8::decode(decoder)?;
        }
        let flags = u32::decode(decoder)?;
        Ok(Self {
            world_product,
            update_time,
            world_rate,
            town_rate,
            item_rate,
            flags,
        })
    }
}
