/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::Reader;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[repr(u8)]
#[derive(Debug, Clone)]
pub(crate) enum PartyReplyType {
    NotFound = 0,
    Busy = 1,
    AcceptCreate = 2,
    AcceptJoin = 3,
    RejectJoin = 4,
    Destroy = 5,
    Full = 6,
    InvalidLevel = 7,
    ChangeOwner = 8,
    ChangeOwnerAndLeave = 9,
    NoPremium = 10,
    Bad = 128,
    Leave = 129,
    Rejoin = 130,
}

impl Encode for PartyReplyType {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        encoder.writer().write(&[self.clone() as u8]).map_err(Into::into)
    }
}

impl<Context> Decode<Context> for PartyReplyType {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let value = u8::decode(decoder)?;
        match value {
            0 => Ok(PartyReplyType::NotFound),
            1 => Ok(PartyReplyType::Busy),
            2 => Ok(PartyReplyType::AcceptCreate),
            3 => Ok(PartyReplyType::AcceptJoin),
            4 => Ok(PartyReplyType::RejectJoin),
            5 => Ok(PartyReplyType::Destroy),
            6 => Ok(PartyReplyType::Full),
            7 => Ok(PartyReplyType::InvalidLevel),
            8 => Ok(PartyReplyType::ChangeOwner),
            9 => Ok(PartyReplyType::ChangeOwnerAndLeave),
            10 => Ok(PartyReplyType::NoPremium),
            128 => Ok(PartyReplyType::Bad),
            129 => Ok(PartyReplyType::Leave),
            130 => Ok(PartyReplyType::Rejoin),
            _ => Err(bincode::error::DecodeError::OtherString(format!(
                "Invalid value for PartyReplyType: {}",
                value
            ))),
        }
    }
}

#[derive(Debug)]
pub struct CliPartyReply {
    pub(crate) type_: PartyReplyType,
    pub(crate) target: u32,
}

impl PacketPayload for CliPartyReply {}

impl Encode for CliPartyReply {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.type_.encode(encoder)?;
        self.target.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for CliPartyReply {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let type_ = PartyReplyType::decode(decoder)?;
        let target = u32::decode(decoder)?;
        Ok(Self { type_, target })
    }
}
