/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::<PERSON>;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[derive(Debug)]
pub struct SrvLogoutReply {
    pub(crate) wait_time: u16,
}

impl PacketPayload for SrvLogoutReply {}

impl Encode for SrvLogoutReply {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.wait_time.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for SrvLogoutReply {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let wait_time = u16::decode(decoder)?;
        Ok(Self { wait_time })
    }
}
