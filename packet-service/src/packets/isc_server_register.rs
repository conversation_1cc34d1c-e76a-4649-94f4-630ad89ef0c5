/* This file is @generated with IDL v0.2.2 */

use bincode::{Encode, Decode, enc::Encoder, de::Decoder, error::DecodeError};
use bincode::de::read::Reader;
use bincode::enc::write::Writer;
use utils::null_string::NullTerminatedString;
use crate::enums::*;
use crate::types::*;
use crate::dataconsts::*;
use crate::packet::PacketPayload;


#[derive(Debug)]
pub struct IscServerRegister {
    pub(crate) server_type: Isc::ServerType, 
    pub(crate) name: NullTerminatedString, 
    pub(crate) addr: NullTerminatedString, 
    pub(crate) port: i32, 
    pub(crate) right: i32, 
    pub(crate) id: i32, 
    // All the maps this client is responsible for
    pub(crate) maps: Vec<u16>, 
}

impl PacketPayload for IscServerRegister {}

impl Encode for IscServerRegister {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.server_type.encode(encoder)?;
        self.name.encode(encoder)?;
        self.addr.encode(encoder)?;
        self.port.encode(encoder)?;
        self.right.encode(encoder)?;
        self.id.encode(encoder)?;
        self.maps.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for IscServerRegister {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let server_type = Isc::ServerType::decode(decoder)?;
        let name = NullTerminatedString::decode(decoder)?;
        let addr = NullTerminatedString::decode(decoder)?;
        let port = i32::decode(decoder)?;
        let right = i32::decode(decoder)?;
        let id = i32::decode(decoder)?;
        let maps = Vec::decode(decoder)?;
        Ok(Self { server_type, name, addr, port, right, id, maps })
    }
}
