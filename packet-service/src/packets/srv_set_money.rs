/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::<PERSON>;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[derive(Debug)]
pub struct SrvSetMoney {
    pub(crate) zuly: i64,
}

impl PacketPayload for SrvSetMoney {}

impl Encode for SrvSetMoney {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.zuly.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for SrvSetMoney {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let zuly = i64::decode(decoder)?;
        Ok(Self { zuly })
    }
}
