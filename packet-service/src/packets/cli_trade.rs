/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::Reader;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[repr(u8)]
#[derive(Debug, Clone)]
pub(crate) enum TradeState {
    Request = 0,
    Accept = 1,
    Reject = 2,
    Cancel = 3,
    Ready = 4,
    Notready = 5,
    Done = 6,
    Busy = 7,
    TooFar = 8,
    InvalidTarget = 9,
    NoSpace = 10,
    TradeDisabled = 11,
}

impl Encode for TradeState {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        encoder.writer().write(&[self.clone() as u8]).map_err(Into::into)
    }
}

impl<Context> Decode<Context> for TradeState {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let value = u8::decode(decoder)?;
        match value {
            0 => Ok(TradeState::Request),
            1 => Ok(TradeState::Accept),
            2 => Ok(TradeState::Reject),
            3 => Ok(TradeState::Cancel),
            4 => Ok(TradeState::Ready),
            5 => Ok(TradeState::Notready),
            6 => Ok(TradeState::Done),
            7 => Ok(TradeState::Busy),
            8 => Ok(TradeState::TooFar),
            9 => Ok(TradeState::InvalidTarget),
            10 => Ok(TradeState::NoSpace),
            11 => Ok(TradeState::TradeDisabled),
            _ => Err(bincode::error::DecodeError::OtherString(format!(
                "Invalid value for TradeState: {}",
                value
            ))),
        }
    }
}

#[derive(Debug)]
pub struct CliTrade {
    pub(crate) state: TradeState,
    pub(crate) target: u16,
    pub(crate) index: u8,
}

impl PacketPayload for CliTrade {}

impl Encode for CliTrade {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.state.encode(encoder)?;
        self.target.encode(encoder)?;
        self.index.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for CliTrade {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let state = TradeState::decode(decoder)?;
        let target = u16::decode(decoder)?;
        let index = u8::decode(decoder)?;
        Ok(Self { state, target, index })
    }
}
