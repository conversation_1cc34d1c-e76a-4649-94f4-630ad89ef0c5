/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::Reader;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[repr(u8)]
#[derive(Debug, Clone)]
pub(crate) enum PartyReqType {
    Create = 0,
    Join = 1,
    Leave = 2,
    ChangeOwner = 3,
    Ban = 129,
}

impl Encode for PartyReqType {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        encoder.writer().write(&[self.clone() as u8]).map_err(Into::into)
    }
}

impl<Context> Decode<Context> for PartyReqType {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let value = u8::decode(decoder)?;
        match value {
            0 => Ok(PartyReqType::Create),
            1 => Ok(PartyReqType::Join),
            2 => Ok(PartyReqType::Leave),
            3 => Ok(PartyReqType::ChangeOwner),
            129 => Ok(PartyReqType::Ban),
            _ => Err(bincode::error::DecodeError::OtherString(format!(
                "Invalid value for PartyReqType: {}",
                value
            ))),
        }
    }
}

#[derive(Debug)]
pub struct SrvPartyReq {
    pub(crate) type_: PartyReqType,
    pub(crate) source: u32,
    pub(crate) name: NullTerminatedString,
}

impl PacketPayload for SrvPartyReq {}

impl Encode for SrvPartyReq {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.type_.encode(encoder)?;
        self.source.encode(encoder)?;
        self.name.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for SrvPartyReq {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let type_ = PartyReqType::decode(decoder)?;
        let source = u32::decode(decoder)?;
        let name = NullTerminatedString::decode(decoder)?;
        Ok(Self { type_, source, name })
    }
}
