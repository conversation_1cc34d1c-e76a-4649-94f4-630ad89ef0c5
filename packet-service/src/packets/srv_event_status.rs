/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::<PERSON>;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[derive(Debug)]
pub struct SrvEventStatus {
    pub(crate) object_id: u16,
    pub(crate) status: i16,
}

impl PacketPayload for SrvEventStatus {}

impl Encode for SrvEventStatus {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.object_id.encode(encoder)?;
        self.status.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for SrvEventStatus {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let object_id = u16::decode(decoder)?;
        let status = i16::decode(decoder)?;
        Ok(Self { object_id, status })
    }
}
