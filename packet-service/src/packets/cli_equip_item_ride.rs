/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::Reader;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[repr(u16)]
#[derive(Debug, Clone)]
pub(crate) enum EquippedPositionRide {
    Body = 0,
    Engine = 1,
    Leg = 2,
    Ability = 3,
    Arm = 4,
}

impl Encode for EquippedPositionRide {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        encoder.writer().write(&[self.clone() as u8]).map_err(Into::into)
    }
}

impl<Context> Decode<Context> for EquippedPositionRide {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let value = u16::decode(decoder)?;
        match value {
            0 => Ok(EquippedPositionRide::Body),
            1 => Ok(EquippedPositionRide::Engine),
            2 => Ok(EquippedPositionRide::Leg),
            3 => Ok(EquippedPositionRide::Ability),
            4 => Ok(EquippedPositionRide::Arm),
            _ => Err(bincode::error::DecodeError::OtherString(format!(
                "Invalid value for EquippedPositionRide: {}",
                value
            ))),
        }
    }
}

#[derive(Debug)]
pub struct CliEquipItemRide {
    pub(crate) slot: EquippedPositionRide,
    pub(crate) index: u16,
}

impl PacketPayload for CliEquipItemRide {}

impl Encode for CliEquipItemRide {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.slot.encode(encoder)?;
        self.index.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for CliEquipItemRide {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let slot = EquippedPositionRide::decode(decoder)?;
        let index = u16::decode(decoder)?;
        Ok(Self { slot, index })
    }
}
