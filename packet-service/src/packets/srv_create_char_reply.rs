/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::Reader;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[repr(u8)]
#[derive(Debug, Clone)]
pub(crate) enum Result {
    Ok = 0,
    Failed = 1,
    NameTaken = 2,
    InvalidValue = 3,
    TooManyChars = 4,
    Blocked = 5,
}

impl Encode for Result {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        encoder.writer().write(&[self.clone() as u8]).map_err(Into::into)
    }
}

impl<Context> Decode<Context> for Result {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let value = u8::decode(decoder)?;
        match value {
            0 => Ok(Result::Ok),
            1 => Ok(Result::Failed),
            2 => Ok(Result::NameTaken),
            3 => Ok(Result::InvalidValue),
            4 => Ok(Result::TooManyChars),
            5 => Ok(Result::Blocked),
            _ => Err(bincode::error::DecodeError::OtherString(format!(
                "Invalid value for Result: {}",
                value
            ))),
        }
    }
}

#[derive(Debug)]
pub struct SrvCreateCharReply {
    pub(crate) result: Result,
    pub(crate) platininum: u8,
}

impl PacketPayload for SrvCreateCharReply {}

impl Encode for SrvCreateCharReply {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.result.encode(encoder)?;
        self.platininum.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for SrvCreateCharReply {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let result = Result::decode(decoder)?;
        let platininum = u8::decode(decoder)?;
        Ok(Self { result, platininum })
    }
}
