/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::Reader;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[derive(Debug, <PERSON><PERSON>, Default)]
pub struct ChannelInfo {
    pub(crate) id: u8,
    pub(crate) low_age: u8,
    pub(crate) high_age: u8,
    pub(crate) capacity: u16,
    pub(crate) name: NullTerminatedString,
}

impl Encode for ChannelInfo {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.id.encode(encoder)?;
        self.low_age.encode(encoder)?;
        self.high_age.encode(encoder)?;
        self.capacity.encode(encoder)?;
        self.name.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for ChannelInfo {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let id = u8::decode(decoder)?;
        let low_age = u8::decode(decoder)?;
        let high_age = u8::decode(decoder)?;
        let capacity = u16::decode(decoder)?;
        let name = NullTerminatedString::decode(decoder)?;
        Ok(Self {
            id,
            low_age,
            high_age,
            capacity,
            name,
        })
    }
}

#[derive(Debug)]
pub struct SrvChannelListReply {
    pub(crate) id: u32,
    pub(crate) channels: Vec<ChannelInfo>,
}

impl PacketPayload for SrvChannelListReply {}

impl Encode for SrvChannelListReply {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.id.encode(encoder)?;
        self.channels.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for SrvChannelListReply {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let id = u32::decode(decoder)?;
        let channels = Vec::decode(decoder)?;
        Ok(Self { id, channels })
    }
}
