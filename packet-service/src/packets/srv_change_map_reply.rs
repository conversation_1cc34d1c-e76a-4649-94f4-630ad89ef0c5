/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::<PERSON>;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[derive(Debug)]
pub struct SrvChangeMapReply {
    pub(crate) object_index: u16,
    pub(crate) hp: u16,
    pub(crate) mp: u16,
    pub(crate) xp: u16,
    pub(crate) penalize_xp: u16,
    pub(crate) craft_rate: u16,
    pub(crate) update_time: u16,
    pub(crate) world_rate: u16,
    pub(crate) town_rate: u8,
    pub(crate) item_rate: [u8; (MAX_SELL_TYPE as usize)],
    pub(crate) flags: u32,
    pub(crate) world_time: u16,
    pub(crate) team_number: u16,
}

impl PacketPayload for SrvChangeMapReply {}

impl Encode for SrvChangeMapReply {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.object_index.encode(encoder)?;
        self.hp.encode(encoder)?;
        self.mp.encode(encoder)?;
        self.xp.encode(encoder)?;
        self.penalize_xp.encode(encoder)?;
        self.craft_rate.encode(encoder)?;
        self.update_time.encode(encoder)?;
        self.world_rate.encode(encoder)?;
        self.town_rate.encode(encoder)?;
        for value in &self.item_rate {
            value.encode(encoder)?;
        }
        self.flags.encode(encoder)?;
        self.world_time.encode(encoder)?;
        self.team_number.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for SrvChangeMapReply {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let object_index = u16::decode(decoder)?;
        let hp = u16::decode(decoder)?;
        let mp = u16::decode(decoder)?;
        let xp = u16::decode(decoder)?;
        let penalize_xp = u16::decode(decoder)?;
        let craft_rate = u16::decode(decoder)?;
        let update_time = u16::decode(decoder)?;
        let world_rate = u16::decode(decoder)?;
        let town_rate = u8::decode(decoder)?;
        let mut item_rate = [0u8; (MAX_SELL_TYPE as usize)];
        for value in &mut item_rate {
            *value = u8::decode(decoder)?;
        }
        let flags = u32::decode(decoder)?;
        let world_time = u16::decode(decoder)?;
        let team_number = u16::decode(decoder)?;
        Ok(Self {
            object_index,
            hp,
            mp,
            xp,
            penalize_xp,
            craft_rate,
            update_time,
            world_rate,
            town_rate,
            item_rate,
            flags,
            world_time,
            team_number,
        })
    }
}
