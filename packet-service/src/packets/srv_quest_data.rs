/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::<PERSON>;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[derive(Debug, Clone, Default)]
pub struct Header {
    pub(crate) type_: u8,
    pub(crate) id: u16,
    pub(crate) is_created: u8,
}

impl Encode for Header {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.type_.encode(encoder)?;
        self.id.encode(encoder)?;
        self.is_created.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for Header {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let type_ = u8::decode(decoder)?;
        let id = u16::decode(decoder)?;
        let is_created = u8::decode(decoder)?;
        Ok(Self { type_, id, is_created })
    }
}

#[derive(Debug, Clone, Default)]
pub struct Data {
    pub(crate) gem_opt: u16,
    pub(crate) life: u16,
    pub(crate) durability: u8,
    pub(crate) has_socket: u8,
    pub(crate) is_appraised: u8,
    pub(crate) refine: u8,
    pub(crate) count: u32,
}

impl Encode for Data {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.gem_opt.encode(encoder)?;
        self.life.encode(encoder)?;
        self.durability.encode(encoder)?;
        self.has_socket.encode(encoder)?;
        self.is_appraised.encode(encoder)?;
        self.refine.encode(encoder)?;
        self.count.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for Data {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let gem_opt = u16::decode(decoder)?;
        let life = u16::decode(decoder)?;
        let durability = u8::decode(decoder)?;
        let has_socket = u8::decode(decoder)?;
        let is_appraised = u8::decode(decoder)?;
        let refine = u8::decode(decoder)?;
        let count = u32::decode(decoder)?;
        Ok(Self {
            gem_opt,
            life,
            durability,
            has_socket,
            is_appraised,
            refine,
            count,
        })
    }
}

#[derive(Debug, Clone, Default)]
pub struct Item {
    pub(crate) header: Header,
    pub(crate) data: Data,
}

impl Encode for Item {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.header.encode(encoder)?;
        self.data.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for Item {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let header = Header::decode(decoder)?;
        let data = Data::decode(decoder)?;
        Ok(Self { header, data })
    }
}

#[derive(Debug, Clone, Default)]
pub struct Quest {
    pub(crate) id: u16,
    // timer: Unlimited if 0
    pub(crate) timer: u32,
    pub(crate) vars: [u16; (MAX_QUEST_VARS as usize)],
    pub(crate) switches: u32,
    pub(crate) items: [Item; (MAX_QUEST_ITEMS as usize)],
}

impl Encode for Quest {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.id.encode(encoder)?;
        self.timer.encode(encoder)?;
        self.vars.encode(encoder)?;
        self.switches.encode(encoder)?;
        self.items.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for Quest {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let id = u16::decode(decoder)?;
        let timer = u32::decode(decoder)?;
        let mut vars = [0u16; (MAX_QUEST_VARS as usize)];
        for value in &mut vars {
            *value = u16::decode(decoder)?;
        }
        let switches = u32::decode(decoder)?;
        let mut items: [Item; (MAX_QUEST_ITEMS as usize)] = core::array::from_fn(|i| Item::default());
        for index in 0..MAX_QUEST_ITEMS as usize {
            items[index] = Item::decode(decoder)?;
        }
        Ok(Self {
            id,
            timer,
            vars,
            switches,
            items,
        })
    }
}

#[derive(Debug)]
pub struct SrvQuestData {
    pub(crate) episodes: [u16; (MAX_CONDITIONS_EPISODE as usize)],
    pub(crate) jobs: [u16; (MAX_CONDITIONS_JOB as usize)],
    pub(crate) planets: [u16; (MAX_CONDITIONS_PLANET as usize)],
    pub(crate) unions: [u16; (MAX_CONDITIONS_UNION as usize)],
    pub(crate) quests: [Quest; (MAX_QUESTS as usize)],
    pub(crate) switches: [u32; (MAX_SWITCHES as usize)],
    pub(crate) wishlist: [Item; (MAX_WISHLIST as usize)],
}

impl PacketPayload for SrvQuestData {}

impl Encode for SrvQuestData {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        for value in &self.episodes {
            value.encode(encoder)?;
        }
        for value in &self.jobs {
            value.encode(encoder)?;
        }
        for value in &self.planets {
            value.encode(encoder)?;
        }
        for value in &self.unions {
            value.encode(encoder)?;
        }
        for value in &self.quests {
            value.encode(encoder)?;
        }
        for value in &self.switches {
            value.encode(encoder)?;
        }
        for value in &self.wishlist {
            value.encode(encoder)?;
        }
        Ok(())
    }
}

impl<Context> Decode<Context> for SrvQuestData {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let mut episodes = [0u16; (MAX_CONDITIONS_EPISODE as usize)];
        for value in &mut episodes {
            *value = u16::decode(decoder)?;
        }
        let mut jobs = [0u16; (MAX_CONDITIONS_JOB as usize)];
        for value in &mut jobs {
            *value = u16::decode(decoder)?;
        }
        let mut planets = [0u16; (MAX_CONDITIONS_PLANET as usize)];
        for value in &mut planets {
            *value = u16::decode(decoder)?;
        }
        let mut unions = [0u16; (MAX_CONDITIONS_UNION as usize)];
        for value in &mut unions {
            *value = u16::decode(decoder)?;
        }
        let mut quests: [Quest; (MAX_QUESTS as usize)] = core::array::from_fn(|i| Quest::default());
        for index in 0..MAX_QUESTS as usize {
            quests[index] = Quest::decode(decoder)?;
        }
        let mut switches = [0u32; (MAX_SWITCHES as usize)];
        for value in &mut switches {
            *value = u32::decode(decoder)?;
        }
        let mut wishlist: [Item; (MAX_WISHLIST as usize)] = core::array::from_fn(|i| Item::default());
        for index in 0..MAX_WISHLIST as usize {
            wishlist[index] = Item::decode(decoder)?;
        }
        Ok(Self {
            episodes,
            jobs,
            planets,
            unions,
            quests,
            switches,
            wishlist,
        })
    }
}
