/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::<PERSON>;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[derive(Debug, Clone, Default)]
pub struct Header {
    pub(crate) type_: u8,
    pub(crate) id: u16,
    pub(crate) is_created: u8,
}

impl Encode for Header {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.type_.encode(encoder)?;
        self.id.encode(encoder)?;
        self.is_created.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for Header {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let type_ = u8::decode(decoder)?;
        let id = u16::decode(decoder)?;
        let is_created = u8::decode(decoder)?;
        Ok(Self { type_, id, is_created })
    }
}

#[derive(Debug, Clone, Default)]
pub struct Data {
    pub(crate) gem_opt: u16,
    pub(crate) life: u16,
    pub(crate) durability: u8,
    pub(crate) has_socket: u8,
    pub(crate) is_appraised: u8,
    pub(crate) refine: u8,
    pub(crate) count: u32,
}

impl Encode for Data {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.gem_opt.encode(encoder)?;
        self.life.encode(encoder)?;
        self.durability.encode(encoder)?;
        self.has_socket.encode(encoder)?;
        self.is_appraised.encode(encoder)?;
        self.refine.encode(encoder)?;
        self.count.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for Data {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let gem_opt = u16::decode(decoder)?;
        let life = u16::decode(decoder)?;
        let durability = u8::decode(decoder)?;
        let has_socket = u8::decode(decoder)?;
        let is_appraised = u8::decode(decoder)?;
        let refine = u8::decode(decoder)?;
        let count = u32::decode(decoder)?;
        Ok(Self {
            gem_opt,
            life,
            durability,
            has_socket,
            is_appraised,
            refine,
            count,
        })
    }
}

#[derive(Debug, Clone, Default)]
pub struct Item {
    pub(crate) header: Header,
    pub(crate) data: Data,
}

impl Encode for Item {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.header.encode(encoder)?;
        self.data.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for Item {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let header = Header::decode(decoder)?;
        let data = Data::decode(decoder)?;
        Ok(Self { header, data })
    }
}

#[derive(Debug, Clone, Default)]
pub struct IndexAndItem {
    pub(crate) index: u8,
    pub(crate) item: Item,
}

impl Encode for IndexAndItem {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.index.encode(encoder)?;
        self.item.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for IndexAndItem {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let index = u8::decode(decoder)?;
        let item = Item::decode(decoder)?;
        Ok(Self { index, item })
    }
}

#[derive(Debug)]
pub struct SrvSetMoneyAndItem {
    pub(crate) zuly: i64,
    pub(crate) items: Vec<IndexAndItem>,
}

impl PacketPayload for SrvSetMoneyAndItem {}

impl Encode for SrvSetMoneyAndItem {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.zuly.encode(encoder)?;
        self.items.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for SrvSetMoneyAndItem {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let zuly = i64::decode(decoder)?;
        let items = Vec::decode(decoder)?;
        Ok(Self { zuly, items })
    }
}
