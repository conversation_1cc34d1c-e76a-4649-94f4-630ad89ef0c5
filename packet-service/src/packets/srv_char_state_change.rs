/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::<PERSON>;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[derive(Debug)]
pub struct SrvCharStateChange {
    pub(crate) target: u16,
    pub(crate) flag: u32,
}

impl PacketPayload for SrvCharStateChange {}

impl Encode for SrvCharStateChange {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.target.encode(encoder)?;
        self.flag.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for SrvCharStateChange {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let target = u16::decode(decoder)?;
        let flag = u32::decode(decoder)?;
        Ok(Self { target, flag })
    }
}
