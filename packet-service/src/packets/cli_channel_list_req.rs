/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::<PERSON>;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[derive(Debug)]
pub struct CliChannelListReq {
    pub(crate) server_id: u32,
}

impl PacketPayload for CliChannelListReq {}

impl Encode for CliChannelListReq {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.server_id.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for CliChannelListReq {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let server_id = u32::decode(decoder)?;
        Ok(Self { server_id })
    }
}
