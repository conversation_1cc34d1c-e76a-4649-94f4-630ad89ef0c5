/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::<PERSON>;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[repr(u8)]
#[derive(Debug, Clone)]
pub(crate) enum CraftStatus {
    CraftStart = 0,
    CraftSuccess = 1,
    CraftFail = 2,
    UpgradeStart = 3,
    UpgradeSuccess = 4,
    UpgradeFail = 5,
}

impl Encode for CraftStatus {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        encoder.writer().write(&[self.clone() as u8]).map_err(Into::into)
    }
}

impl<Context> Decode<Context> for CraftStatus {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let value = u8::decode(decoder)?;
        match value {
            0 => Ok(CraftStatus::CraftStart),
            1 => Ok(CraftStatus::CraftSuccess),
            2 => Ok(CraftStatus::CraftFail),
            3 => Ok(CraftStatus::UpgradeStart),
            4 => Ok(CraftStatus::UpgradeSuccess),
            5 => Ok(CraftStatus::UpgradeFail),
            _ => Err(bincode::error::DecodeError::OtherString(format!(
                "Invalid value for CraftStatus: {}",
                value
            ))),
        }
    }
}

#[derive(Debug)]
pub struct SrvCraftStatus {
    pub(crate) target: u16,
    pub(crate) stats: CraftStatus,
    pub(crate) type_: i8,
    pub(crate) id: i16,
}

impl PacketPayload for SrvCraftStatus {}

impl Encode for SrvCraftStatus {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.target.encode(encoder)?;
        self.stats.encode(encoder)?;
        self.type_.encode(encoder)?;
        self.id.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for SrvCraftStatus {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let target = u16::decode(decoder)?;
        let stats = CraftStatus::decode(decoder)?;
        let type_ = i8::decode(decoder)?;
        let id = i16::decode(decoder)?;
        Ok(Self {
            target,
            stats,
            type_,
            id,
        })
    }
}
