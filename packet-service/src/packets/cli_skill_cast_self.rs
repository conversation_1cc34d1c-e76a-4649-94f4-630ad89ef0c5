/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::<PERSON>;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[derive(Debug)]
pub struct CliSkillCastSelf {
    pub(crate) slot: u8,
}

impl PacketPayload for CliSkillCastSelf {}

impl Encode for CliSkillCastSelf {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.slot.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for CliSkillCastSelf {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let slot = u8::decode(decoder)?;
        Ok(Self { slot })
    }
}
