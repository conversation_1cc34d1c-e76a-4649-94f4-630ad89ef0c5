/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::Reader;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[derive(Debug)]
pub struct SrvSwitchServer {
    pub(crate) port: u16,
    pub(crate) session_id: u32,
    pub(crate) session_seed: u32,
    pub(crate) ip: NullTerminatedString,
}

impl PacketPayload for SrvSwitchServer {}

impl Encode for SrvSwitchServer {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.port.encode(encoder)?;
        self.session_id.encode(encoder)?;
        self.session_seed.encode(encoder)?;
        self.ip.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for SrvSwitchServer {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let port = u16::decode(decoder)?;
        let session_id = u32::decode(decoder)?;
        let session_seed = u32::decode(decoder)?;
        let ip = NullTerminatedString::decode(decoder)?;
        Ok(Self {
            port,
            session_id,
            session_seed,
            ip,
        })
    }
}
