/* This file is @generated with IDL v0.2.2 */

use bincode::{Encode, Decode, enc::Encoder, de::Decoder, error::DecodeError};
use bincode::de::read::Reader;
use bincode::enc::write::Writer;
use utils::null_string::NullTerminatedString;
use crate::enums::*;
use crate::types::*;
use crate::dataconsts::*;
use crate::packet::PacketPayload;


#[derive(Debug)]
pub struct IscTransfer {
    pub(crate) server_packet: u8, 
    pub(crate) originator_id: u32, 
    pub(crate) maps: Vec<u16>, 
    pub(crate) blob: Vec<u8>, 
}

impl PacketPayload for IscTransfer {}

impl Encode for IscTransfer {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.server_packet.encode(encoder)?;
        self.originator_id.encode(encoder)?;
        self.maps.encode(encoder)?;
        self.blob.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for IscTransfer {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let server_packet = u8::decode(decoder)?;
        let originator_id = u32::decode(decoder)?;
        let maps = Vec::decode(decoder)?;
        let blob = Vec::decode(decoder)?;
        Ok(Self { server_packet, originator_id, maps, blob })
    }
}
