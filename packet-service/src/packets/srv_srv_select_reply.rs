/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::Reader;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[repr(u8)]
#[derive(Debug, Clone)]
pub(crate) enum Result {
    Ok = 0,
    Failed = 1,
    Full = 2,
    InvalidChannel = 3,
    ChannelNotActive = 4,
    InvalidAge = 5,
}

impl Encode for Result {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        encoder.writer().write(&[self.clone() as u8]).map_err(Into::into)
    }
}

impl<Context> Decode<Context> for Result {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let value = u8::decode(decoder)?;
        match value {
            0 => Ok(Result::Ok),
            1 => Ok(Result::Failed),
            2 => Ok(Result::Full),
            3 => Ok(Result::InvalidChannel),
            4 => Ok(Result::ChannelNotActive),
            5 => Ok(Result::InvalidAge),
            _ => Err(bincode::error::DecodeError::OtherString(format!(
                "Invalid value for Result: {}",
                value
            ))),
        }
    }
}

#[derive(Debug)]
pub struct SrvSrvSelectReply {
    pub(crate) result: Result,
    pub(crate) session_id: u32,
    pub(crate) crypt_val: u32,
    pub(crate) ip: NullTerminatedString,
    pub(crate) port: u16,
}

impl PacketPayload for SrvSrvSelectReply {}

impl Encode for SrvSrvSelectReply {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.result.encode(encoder)?;
        self.session_id.encode(encoder)?;
        self.crypt_val.encode(encoder)?;
        self.ip.encode(encoder)?;
        self.port.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for SrvSrvSelectReply {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let result = Result::decode(decoder)?;
        let session_id = u32::decode(decoder)?;
        let crypt_val = u32::decode(decoder)?;
        let ip = NullTerminatedString::decode(decoder)?;
        let port = u16::decode(decoder)?;
        Ok(Self {
            result,
            session_id,
            crypt_val,
            ip,
            port,
        })
    }
}
