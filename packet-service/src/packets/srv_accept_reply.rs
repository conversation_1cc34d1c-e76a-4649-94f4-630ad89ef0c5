/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::Reader;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[repr(u8)]
#[derive(Debug, Clone)]
pub(crate) enum Result {
    Connect = 1,
    Accepted = 2,
    Disconnect = 3,
    Derverdead = 4,
}

impl Encode for Result {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        encoder.writer().write(&[self.clone() as u8]).map_err(Into::into)
    }
}

impl<Context> Decode<Context> for Result {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let value = u8::decode(decoder)?;
        match value {
            1 => Ok(Result::Connect),
            2 => Ok(Result::Accepted),
            3 => Ok(Result::Disconnect),
            4 => Ok(Result::Derverdead),
            _ => Err(bincode::error::DecodeError::OtherString(format!(
                "Invalid value for Result: {}",
                value
            ))),
        }
    }
}

#[derive(Debug)]
pub struct SrvAcceptReply {
    pub(crate) result: Result,
    pub(crate) rand_value: u32,
}

impl PacketPayload for SrvAcceptReply {}

impl Encode for SrvAcceptReply {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.result.encode(encoder)?;
        self.rand_value.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for SrvAcceptReply {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let result = Result::decode(decoder)?;
        let rand_value = u32::decode(decoder)?;
        Ok(Self { result, rand_value })
    }
}
