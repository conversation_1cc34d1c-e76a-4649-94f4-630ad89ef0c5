/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::Reader;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[repr(u8)]
#[derive(Debug, Clone)]
pub(crate) enum SkillLearnResult {
    Failed = 0,
    Succeeded = 1,
    NeedJob = 2,
    NeedSkill = 3,
    NeedStat = 4,
    NoSlots = 5,
    InvalidSkill = 6,
    NoPoints = 7,
    Delete = 8,
}

impl Encode for SkillLearnResult {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        encoder.writer().write(&[self.clone() as u8]).map_err(Into::into)
    }
}

impl<Context> Decode<Context> for SkillLearnResult {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let value = u8::decode(decoder)?;
        match value {
            0 => Ok(SkillLearnResult::Failed),
            1 => Ok(SkillLearnResult::Succeeded),
            2 => Ok(SkillLearnResult::NeedJob),
            3 => Ok(SkillLearnResult::NeedSkill),
            4 => Ok(SkillLearnResult::NeedStat),
            5 => Ok(SkillLearnResult::NoSlots),
            6 => Ok(SkillLearnResult::InvalidSkill),
            7 => Ok(SkillLearnResult::NoPoints),
            8 => Ok(SkillLearnResult::Delete),
            _ => Err(bincode::error::DecodeError::OtherString(format!(
                "Invalid value for SkillLearnResult: {}",
                value
            ))),
        }
    }
}

#[derive(Debug)]
pub struct SrvSkillLearn {
    pub(crate) result: SkillLearnResult,
    pub(crate) slot: u8,
    pub(crate) id: i16,
    pub(crate) points: i16,
}

impl PacketPayload for SrvSkillLearn {}

impl Encode for SrvSkillLearn {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.result.encode(encoder)?;
        self.slot.encode(encoder)?;
        self.id.encode(encoder)?;
        self.points.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for SrvSkillLearn {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let result = SkillLearnResult::decode(decoder)?;
        let slot = u8::decode(decoder)?;
        let id = i16::decode(decoder)?;
        let points = i16::decode(decoder)?;
        Ok(Self {
            result,
            slot,
            id,
            points,
        })
    }
}
