/* This file is @generated with IDL v0.2.2 */

use bincode::{Encode, Decode, enc::Encoder, de::Decoder, error::DecodeError};
use bincode::de::read::Reader;
use bincode::enc::write::Writer;
use utils::null_string::NullTerminatedString;
use crate::enums::*;
use crate::types::*;
use crate::dataconsts::*;
use crate::packet::PacketPayload;


#[derive(Debug)]
pub struct IscAlive {
}

impl PacketPayload for IscAlive {}

impl Encode for IscAlive {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        Ok(())
    }
}

impl<Context> Decode<Context> for IscAlive {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        Ok(Self {  })
    }
}
