/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::<PERSON>;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[derive(Debug)]
pub struct SrvBankMoveMoney {
    pub(crate) inventory: i64,
    pub(crate) bank: i64,
}

impl PacketPayload for SrvBankMoveMoney {}

impl Encode for SrvBankMoveMoney {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.inventory.encode(encoder)?;
        self.bank.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for SrvBankMoveMoney {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let inventory = i64::decode(decoder)?;
        let bank = i64::decode(decoder)?;
        Ok(Self { inventory, bank })
    }
}
