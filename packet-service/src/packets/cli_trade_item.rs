/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::Reader;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[derive(Debug)]
pub struct CliTradeItem {
    pub(crate) slot: u8,
    pub(crate) index: u16,
    pub(crate) amount: u32,
}

impl PacketPayload for CliTradeItem {}

impl Encode for CliTradeItem {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.slot.encode(encoder)?;
        self.index.encode(encoder)?;
        self.amount.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for CliTradeItem {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let slot = u8::decode(decoder)?;
        let index = u16::decode(decoder)?;
        let amount = u32::decode(decoder)?;
        Ok(Self { slot, index, amount })
    }
}
