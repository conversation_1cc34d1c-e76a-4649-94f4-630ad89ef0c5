/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::<PERSON>;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[derive(Debug)]
pub struct CliSrvSelectReq {
    pub(crate) server_id: u32,
    pub(crate) channel_id: u8,
}

impl PacketPayload for CliSrvSelectReq {}

impl Encode for CliSrvSelectReq {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.server_id.encode(encoder)?;
        self.channel_id.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for CliSrvSelectReq {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let server_id = u32::decode(decoder)?;
        let channel_id = u8::decode(decoder)?;
        Ok(Self { server_id, channel_id })
    }
}
