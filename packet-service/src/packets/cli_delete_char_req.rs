/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::Reader;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[derive(Debug)]
pub struct CliDeleteCharReq {
    pub(crate) char_id: u8,
    pub(crate) is_delete: u8,
    pub(crate) name: NullTerminatedString,
}

impl PacketPayload for CliDeleteCharReq {}

impl Encode for CliDeleteCharReq {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.char_id.encode(encoder)?;
        self.is_delete.encode(encoder)?;
        self.name.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for CliDeleteCharReq {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let char_id = u8::decode(decoder)?;
        let is_delete = u8::decode(decoder)?;
        let name = NullTerminatedString::decode(decoder)?;
        Ok(Self {
            char_id,
            is_delete,
            name,
        })
    }
}
