/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::<PERSON>;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[derive(Debug)]
pub struct SrvBillingMessage {
    pub(crate) function_type: u16,
    pub(crate) pay_flag: u32,
}

impl PacketPayload for SrvBillingMessage {}

impl Encode for SrvBillingMessage {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.function_type.encode(encoder)?;
        self.pay_flag.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for SrvBillingMessage {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let function_type = u16::decode(decoder)?;
        let pay_flag = u32::decode(decoder)?;
        Ok(Self {
            function_type,
            pay_flag,
        })
    }
}
