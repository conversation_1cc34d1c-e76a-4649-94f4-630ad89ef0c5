/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::Reader;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[repr(u8)]
#[derive(Debug, Clone)]
pub(crate) enum PartyRule {
    ExpPerPlayer = 1,
    ItemInOrder = 128,
}

impl Encode for PartyRule {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        encoder.writer().write(&[self.clone() as u8]).map_err(Into::into)
    }
}

impl<Context> Decode<Context> for PartyRule {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let value = u8::decode(decoder)?;
        match value {
            1 => Ok(PartyRule::ExpPerPlayer),
            128 => Ok(PartyRule::ItemInOrder),
            _ => Err(bincode::error::DecodeError::OtherString(format!(
                "Invalid value for PartyRule: {}",
                value
            ))),
        }
    }
}

#[derive(Debug)]
pub struct CliPartyRule {
    pub(crate) rule: PartyRule,
}

impl PacketPayload for CliPartyRule {}

impl Encode for CliPartyRule {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.rule.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for CliPartyRule {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let rule = PartyRule::decode(decoder)?;
        Ok(Self { rule })
    }
}
