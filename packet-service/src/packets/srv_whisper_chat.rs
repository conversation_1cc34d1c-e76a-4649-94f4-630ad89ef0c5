/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::<PERSON>;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[derive(Debug)]
pub struct SrvWhisperChat {
    pub(crate) sender: NullTerminatedString,
    pub(crate) message: NullTerminatedString,
}

impl PacketPayload for SrvWhisperChat {}

impl Encode for SrvWhisperChat {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.sender.encode(encoder)?;
        self.message.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for SrvWhisperChat {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let sender = NullTerminatedString::decode(decoder)?;
        let message = NullTerminatedString::decode(decoder)?;
        Ok(Self { sender, message })
    }
}
