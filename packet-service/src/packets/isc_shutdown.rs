/* This file is @generated with IDL v0.2.2 */

use bincode::{Encode, Decode, enc::Encoder, de::Decoder, error::DecodeError};
use bincode::de::read::Reader;
use bincode::enc::write::Writer;
use utils::null_string::NullTerminatedString;
use crate::enums::*;
use crate::types::*;
use crate::dataconsts::*;
use crate::packet::PacketPayload;


#[derive(Debug)]
pub struct IscShutdown {
    pub(crate) server_type: Isc::ServerType, 
    pub(crate) id: i32, 
}

impl PacketPayload for IscShutdown {}

impl Encode for IscShutdown {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.server_type.encode(encoder)?;
        self.id.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for IscShutdown {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let server_type = Isc::ServerType::decode(decoder)?;
        let id = i32::decode(decoder)?;
        Ok(Self { server_type, id })
    }
}
