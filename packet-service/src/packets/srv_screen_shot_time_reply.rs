/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::<PERSON>;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[derive(Debug)]
pub struct SrvScreenShotTimeReply {
    pub(crate) year: u16,
    pub(crate) month: u8,
    pub(crate) day: u8,
    pub(crate) hour: u8,
    pub(crate) min: u8,
}

impl PacketPayload for SrvScreenShotTimeReply {}

impl Encode for SrvScreenShotTimeReply {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.year.encode(encoder)?;
        self.month.encode(encoder)?;
        self.day.encode(encoder)?;
        self.hour.encode(encoder)?;
        self.min.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for SrvScreenShotTimeReply {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let year = u16::decode(decoder)?;
        let month = u8::decode(decoder)?;
        let day = u8::decode(decoder)?;
        let hour = u8::decode(decoder)?;
        let min = u8::decode(decoder)?;
        Ok(Self {
            year,
            month,
            day,
            hour,
            min,
        })
    }
}
