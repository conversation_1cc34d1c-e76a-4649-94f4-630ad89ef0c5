/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::Reader;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[repr(u16)]
#[derive(Debug, Clone)]
pub(crate) enum QuestRewardValueType {
    Sex = 2,
    Class = 4,
    Union = 5,
    Face = 8,
    Hair = 9,
    Str = 10,
    Dex = 11,
    Int = 12,
    Con = 13,
    Cha = 14,
    <PERSON> = 15,
    Hp = 16,
    Mp = 17,
    Exp = 30,
    Level = 31,
    PkFlag = 33,
    Team = 34,
    Unionpoint1 = 81,
    Unionpoint2 = 82,
    Unionpoint3 = 83,
    Unionpoint4 = 84,
    Unionpoint5 = 85,
    Unionpoint6 = 86,
    Unionpoint7 = 87,
    Unionpoint8 = 88,
    Unionpoint9 = 89,
    Unionpoint10 = 90,
}

impl Encode for QuestRewardValueType {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        encoder.writer().write(&[self.clone() as u8]).map_err(Into::into)
    }
}

impl<Context> Decode<Context> for QuestRewardValueType {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let value = u16::decode(decoder)?;
        match value {
            2 => Ok(QuestRewardValueType::Sex),
            4 => Ok(QuestRewardValueType::Class),
            5 => Ok(QuestRewardValueType::Union),
            8 => Ok(QuestRewardValueType::Face),
            9 => Ok(QuestRewardValueType::Hair),
            10 => Ok(QuestRewardValueType::Str),
            11 => Ok(QuestRewardValueType::Dex),
            12 => Ok(QuestRewardValueType::Int),
            13 => Ok(QuestRewardValueType::Con),
            14 => Ok(QuestRewardValueType::Cha),
            15 => Ok(QuestRewardValueType::Sen),
            16 => Ok(QuestRewardValueType::Hp),
            17 => Ok(QuestRewardValueType::Mp),
            30 => Ok(QuestRewardValueType::Exp),
            31 => Ok(QuestRewardValueType::Level),
            33 => Ok(QuestRewardValueType::PkFlag),
            34 => Ok(QuestRewardValueType::Team),
            81 => Ok(QuestRewardValueType::Unionpoint1),
            82 => Ok(QuestRewardValueType::Unionpoint2),
            83 => Ok(QuestRewardValueType::Unionpoint3),
            84 => Ok(QuestRewardValueType::Unionpoint4),
            85 => Ok(QuestRewardValueType::Unionpoint5),
            86 => Ok(QuestRewardValueType::Unionpoint6),
            87 => Ok(QuestRewardValueType::Unionpoint7),
            88 => Ok(QuestRewardValueType::Unionpoint8),
            89 => Ok(QuestRewardValueType::Unionpoint9),
            90 => Ok(QuestRewardValueType::Unionpoint10),
            _ => Err(bincode::error::DecodeError::OtherString(format!(
                "Invalid value for QuestRewardValueType: {}",
                value
            ))),
        }
    }
}

#[derive(Debug)]
pub struct SrvQuestRewardSetValue {
    pub(crate) type_: QuestRewardValueType,
    pub(crate) value: i32,
}

impl PacketPayload for SrvQuestRewardSetValue {}

impl Encode for SrvQuestRewardSetValue {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.type_.encode(encoder)?;
        self.value.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for SrvQuestRewardSetValue {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let type_ = QuestRewardValueType::decode(decoder)?;
        let value = i32::decode(decoder)?;
        Ok(Self { type_, value })
    }
}
