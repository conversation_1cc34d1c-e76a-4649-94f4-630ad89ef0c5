/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::<PERSON>;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[derive(Debug, <PERSON><PERSON>, Default)]
pub struct AppraisalReply {
    pub(crate) index: u16,
    pub(crate) result: u8,
}

impl Encode for AppraisalReply {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.index.encode(encoder)?;
        self.result.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for AppraisalReply {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let index = u16::decode(decoder)?;
        let result = u8::decode(decoder)?;
        Ok(Self { index, result })
    }
}

#[derive(Debug)]
pub struct SrvAppraisalReply {
    pub(crate) result: AppraisalReply,
}

impl PacketPayload for SrvAppraisalReply {}

impl Encode for SrvAppraisalReply {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.result.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for SrvAppraisalReply {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let result = AppraisalReply::decode(decoder)?;
        Ok(Self { result })
    }
}
