/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::Reader;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[repr(u8)]
#[derive(Debug, Clone)]
pub(crate) enum EquippedPosition {
    Helmet = 0,
    Armor = 1,
    Gauntlet = 2,
    Boots = 3,
    Googles = 4,
    Backpack = 5,
    WeaponR = 6,
    WeaponL = 7,
    MaxItems = 8,
}

impl Encode for EquippedPosition {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        encoder.writer().write(&[self.clone() as u8]).map_err(Into::into)
    }
}

impl<Context> Decode<Context> for EquippedPosition {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let value = u8::decode(decoder)?;
        match value {
            0 => Ok(EquippedPosition::Helmet),
            1 => Ok(EquippedPosition::Armor),
            2 => Ok(EquippedPosition::Gauntlet),
            3 => Ok(EquippedPosition::Boots),
            4 => Ok(EquippedPosition::Googles),
            5 => Ok(EquippedPosition::Backpack),
            6 => Ok(EquippedPosition::WeaponR),
            7 => Ok(EquippedPosition::WeaponL),
            8 => Ok(EquippedPosition::MaxItems),
            _ => Err(bincode::error::DecodeError::OtherString(format!(
                "Invalid value for EquippedPosition: {}",
                value
            ))),
        }
    }
}

#[derive(Debug, Clone, Default)]
pub struct EquippedItem {
    pub(crate) id: u16,
    pub(crate) gem_opt: u16,
    pub(crate) socket: i8,
    pub(crate) grade: u8,
}

impl Encode for EquippedItem {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.id.encode(encoder)?;
        self.gem_opt.encode(encoder)?;
        self.socket.encode(encoder)?;
        self.grade.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for EquippedItem {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let id = u16::decode(decoder)?;
        let gem_opt = u16::decode(decoder)?;
        let socket = i8::decode(decoder)?;
        let grade = u8::decode(decoder)?;
        Ok(Self {
            id,
            gem_opt,
            socket,
            grade,
        })
    }
}

#[derive(Debug, Clone, Default)]
pub struct CharInfo {
    pub(crate) name: NullTerminatedString,
    pub(crate) race: u8,
    pub(crate) level: u16,
    pub(crate) job: u16,
    pub(crate) remain_secs_until_delete: u32,
    pub(crate) platinium: u8,
    pub(crate) face: u32,
    pub(crate) hair: u32,
    pub(crate) items: [EquippedItem; (MAX_VISIBLE_ITEMS as usize)],
}

impl Encode for CharInfo {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.name.encode(encoder)?;
        self.race.encode(encoder)?;
        self.level.encode(encoder)?;
        self.job.encode(encoder)?;
        self.remain_secs_until_delete.encode(encoder)?;
        self.platinium.encode(encoder)?;
        self.face.encode(encoder)?;
        self.hair.encode(encoder)?;
        self.items.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for CharInfo {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let name = NullTerminatedString::decode(decoder)?;
        let race = u8::decode(decoder)?;
        let level = u16::decode(decoder)?;
        let job = u16::decode(decoder)?;
        let remain_secs_until_delete = u32::decode(decoder)?;
        let platinium = u8::decode(decoder)?;
        let face = u32::decode(decoder)?;
        let hair = u32::decode(decoder)?;
        let mut items: [EquippedItem; (MAX_VISIBLE_ITEMS as usize)] = core::array::from_fn(|i| EquippedItem::default());
        for index in 0..MAX_VISIBLE_ITEMS as usize {
            items[index] = EquippedItem::decode(decoder)?;
        }
        Ok(Self {
            name,
            race,
            level,
            job,
            remain_secs_until_delete,
            platinium,
            face,
            hair,
            items,
        })
    }
}

#[derive(Debug)]
pub struct SrvCharListReply {
    pub(crate) characters: Vec<CharInfo>,
}

impl PacketPayload for SrvCharListReply {}

impl Encode for SrvCharListReply {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.characters.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for SrvCharListReply {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let characters = Vec::decode(decoder)?;
        Ok(Self { characters })
    }
}
