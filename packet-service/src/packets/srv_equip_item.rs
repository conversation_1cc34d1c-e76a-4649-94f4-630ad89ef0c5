/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::Reader;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[derive(Debug, <PERSON>lone, Default)]
pub struct EquippedItem {
    pub(crate) id: u16,
    pub(crate) gem_opt: u16,
    pub(crate) socket: i8,
    pub(crate) grade: u8,
}

impl Encode for EquippedItem {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.id.encode(encoder)?;
        self.gem_opt.encode(encoder)?;
        self.socket.encode(encoder)?;
        self.grade.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for EquippedItem {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let id = u16::decode(decoder)?;
        let gem_opt = u16::decode(decoder)?;
        let socket = i8::decode(decoder)?;
        let grade = u8::decode(decoder)?;
        Ok(Self {
            id,
            gem_opt,
            socket,
            grade,
        })
    }
}

#[derive(Debug)]
pub struct SrvEquipItem {
    pub(crate) id: u16,
    pub(crate) slot: i16,
    pub(crate) item: EquippedItem,
}

impl PacketPayload for SrvEquipItem {}

impl Encode for SrvEquipItem {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.id.encode(encoder)?;
        self.slot.encode(encoder)?;
        self.item.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for SrvEquipItem {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let id = u16::decode(decoder)?;
        let slot = i16::decode(decoder)?;
        let item = EquippedItem::decode(decoder)?;
        Ok(Self { id, slot, item })
    }
}
