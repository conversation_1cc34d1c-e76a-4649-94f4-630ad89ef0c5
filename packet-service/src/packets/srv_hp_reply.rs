/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::<PERSON>;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[derive(Debug)]
pub struct SrvHpReply {
    pub(crate) id: u16,
    pub(crate) hp: i32,
}

impl PacketPayload for SrvHpReply {}

impl Encode for SrvHpReply {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.id.encode(encoder)?;
        self.hp.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for SrvHpReply {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let id = u16::decode(decoder)?;
        let hp = i32::decode(decoder)?;
        Ok(Self { id, hp })
    }
}
