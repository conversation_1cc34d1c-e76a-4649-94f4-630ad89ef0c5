/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::<PERSON>;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[derive(Debug)]
pub struct SrvSetExp {
    pub(crate) exp: i64,
    pub(crate) stamina: i16,
    pub(crate) source_id: u16,
}

impl PacketPayload for SrvSetExp {}

impl Encode for SrvSetExp {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.exp.encode(encoder)?;
        self.stamina.encode(encoder)?;
        self.source_id.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for SrvSetExp {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let exp = i64::decode(decoder)?;
        let stamina = i16::decode(decoder)?;
        let source_id = u16::decode(decoder)?;
        Ok(Self {
            exp,
            stamina,
            source_id,
        })
    }
}
