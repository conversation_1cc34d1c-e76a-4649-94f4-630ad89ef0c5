/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::Reader;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[repr(u8)]
#[derive(Debug, Clone)]
pub(crate) enum RideAction {
    Request = 0,
    Accept = 1,
    Refuse = 2,
    OwnerNotFound = 3,
    GuestNotFound = 4,
}

impl Encode for RideAction {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        encoder.writer().write(&[self.clone() as u8]).map_err(Into::into)
    }
}

impl<Context> Decode<Context> for RideAction {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let value = u8::decode(decoder)?;
        match value {
            0 => Ok(RideAction::Request),
            1 => Ok(RideAction::Accept),
            2 => Ok(RideAction::Refuse),
            3 => Ok(RideAction::OwnerNotFound),
            4 => Ok(RideAction::GuestNotFound),
            _ => Err(bincode::error::DecodeError::OtherString(format!(
                "Invalid value for RideAction: {}",
                value
            ))),
        }
    }
}

#[derive(Debug)]
pub struct CliRideRequest {
    pub(crate) action: RideAction,
    pub(crate) owner: u16,
    pub(crate) guest: u32,
}

impl PacketPayload for CliRideRequest {}

impl Encode for CliRideRequest {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.action.encode(encoder)?;
        self.owner.encode(encoder)?;
        self.guest.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for CliRideRequest {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let action = RideAction::decode(decoder)?;
        let owner = u16::decode(decoder)?;
        let guest = u32::decode(decoder)?;
        Ok(Self { action, owner, guest })
    }
}
