/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::<PERSON>;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[derive(Debug)]
pub struct SrvNpcChar {
    pub(crate) id: u16,
    pub(crate) x: f32,
    pub(crate) y: f32,
    pub(crate) dest_x: f32,
    pub(crate) dest_y: f32,
    pub(crate) command: u16,
    pub(crate) target_id: u16,
    pub(crate) move_mode: u8,
    pub(crate) hp: i32,
    pub(crate) team_id: i32,
    pub(crate) status_flag: u32,
    pub(crate) npc_id: u16,
    pub(crate) quest_id: u16,
    pub(crate) angle: f32,
    pub(crate) event_status: u16,
}

impl PacketPayload for SrvNpcChar {}

impl Encode for SrvNpcChar {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.id.encode(encoder)?;
        self.x.encode(encoder)?;
        self.y.encode(encoder)?;
        self.dest_x.encode(encoder)?;
        self.dest_y.encode(encoder)?;
        self.command.encode(encoder)?;
        self.target_id.encode(encoder)?;
        self.move_mode.encode(encoder)?;
        self.hp.encode(encoder)?;
        self.team_id.encode(encoder)?;
        self.status_flag.encode(encoder)?;
        self.npc_id.encode(encoder)?;
        self.quest_id.encode(encoder)?;
        self.angle.encode(encoder)?;
        self.event_status.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for SrvNpcChar {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let id = u16::decode(decoder)?;
        let x = f32::decode(decoder)?;
        let y = f32::decode(decoder)?;
        let dest_x = f32::decode(decoder)?;
        let dest_y = f32::decode(decoder)?;
        let command = u16::decode(decoder)?;
        let target_id = u16::decode(decoder)?;
        let move_mode = u8::decode(decoder)?;
        let hp = i32::decode(decoder)?;
        let team_id = i32::decode(decoder)?;
        let status_flag = u32::decode(decoder)?;
        let npc_id = u16::decode(decoder)?;
        let quest_id = u16::decode(decoder)?;
        let angle = f32::decode(decoder)?;
        let event_status = u16::decode(decoder)?;
        Ok(Self {
            id,
            x,
            y,
            dest_x,
            dest_y,
            command,
            target_id,
            move_mode,
            hp,
            team_id,
            status_flag,
            npc_id,
            quest_id,
            angle,
            event_status,
        })
    }
}
