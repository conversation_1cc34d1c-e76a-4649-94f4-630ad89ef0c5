/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::Reader;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[derive(Debug)]
pub struct SrvAttack {
    pub(crate) attacker_id: u16,
    pub(crate) defender_id: u16,
    pub(crate) distance: u16,
    pub(crate) x: f32,
    pub(crate) y: f32,
    pub(crate) z: u16,
}

impl PacketPayload for SrvAttack {}

impl Encode for SrvAttack {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.attacker_id.encode(encoder)?;
        self.defender_id.encode(encoder)?;
        self.distance.encode(encoder)?;
        self.x.encode(encoder)?;
        self.y.encode(encoder)?;
        self.z.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for SrvAttack {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let attacker_id = u16::decode(decoder)?;
        let defender_id = u16::decode(decoder)?;
        let distance = u16::decode(decoder)?;
        let x = f32::decode(decoder)?;
        let y = f32::decode(decoder)?;
        let z = u16::decode(decoder)?;
        Ok(Self {
            attacker_id,
            defender_id,
            distance,
            x,
            y,
            z,
        })
    }
}
