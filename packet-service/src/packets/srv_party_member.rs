/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::Reader;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[repr(u8)]
#[derive(Debug, Clone)]
pub(crate) enum PartyRule {
    ExpPerPlayer = 1,
    ItemInOrder = 128,
}

impl Encode for PartyRule {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        encoder.writer().write(&[self.clone() as u8]).map_err(Into::into)
    }
}

impl<Context> Decode<Context> for PartyRule {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let value = u8::decode(decoder)?;
        match value {
            1 => Ok(PartyRule::ExpPerPlayer),
            128 => Ok(PartyRule::ItemInOrder),
            _ => Err(bincode::error::DecodeError::OtherString(format!(
                "Invalid value for PartyRule: {}",
                value
            ))),
        }
    }
}

#[repr(i8)]
#[derive(Debug, Clone)]
pub(crate) enum PartyMemberAction {
    Remove = -1,
    Add = 1,
}

impl Encode for PartyMemberAction {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        encoder.writer().write(&[self.clone() as u8]).map_err(Into::into)
    }
}

impl<Context> Decode<Context> for PartyMemberAction {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let value = i8::decode(decoder)?;
        match value {
            -1 => Ok(PartyMemberAction::Remove),
            1 => Ok(PartyMemberAction::Add),
            _ => Err(bincode::error::DecodeError::OtherString(format!(
                "Invalid value for PartyMemberAction: {}",
                value
            ))),
        }
    }
}

#[derive(Debug, Clone, Default)]
pub struct PartyMember {
    pub(crate) tag: u32,
    pub(crate) id: u16,
    pub(crate) max_hp: u32,
    pub(crate) hp: u32,
    pub(crate) status: u32,
    pub(crate) con: u16,
    pub(crate) hp_recovery: u16,
    pub(crate) mp_recovery: u16,
    pub(crate) stamina: i16,
    pub(crate) name: NullTerminatedString,
}

impl Encode for PartyMember {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.tag.encode(encoder)?;
        self.id.encode(encoder)?;
        self.max_hp.encode(encoder)?;
        self.hp.encode(encoder)?;
        self.status.encode(encoder)?;
        self.con.encode(encoder)?;
        self.hp_recovery.encode(encoder)?;
        self.mp_recovery.encode(encoder)?;
        self.stamina.encode(encoder)?;
        self.name.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for PartyMember {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let tag = u32::decode(decoder)?;
        let id = u16::decode(decoder)?;
        let max_hp = u32::decode(decoder)?;
        let hp = u32::decode(decoder)?;
        let status = u32::decode(decoder)?;
        let con = u16::decode(decoder)?;
        let hp_recovery = u16::decode(decoder)?;
        let mp_recovery = u16::decode(decoder)?;
        let stamina = i16::decode(decoder)?;
        let name = NullTerminatedString::decode(decoder)?;
        Ok(Self {
            tag,
            id,
            max_hp,
            hp,
            status,
            con,
            hp_recovery,
            mp_recovery,
            stamina,
            name,
        })
    }
}

#[derive(Debug)]
pub struct SrvPartyMember {
    pub(crate) rule: PartyRule,
    pub(crate) action: PartyMemberAction,
    pub(crate) member: PartyMember,
}

impl PacketPayload for SrvPartyMember {}

impl Encode for SrvPartyMember {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.rule.encode(encoder)?;
        self.action.encode(encoder)?;
        self.member.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for SrvPartyMember {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let rule = PartyRule::decode(decoder)?;
        let action = PartyMemberAction::decode(decoder)?;
        let member = PartyMember::decode(decoder)?;
        Ok(Self { rule, action, member })
    }
}
