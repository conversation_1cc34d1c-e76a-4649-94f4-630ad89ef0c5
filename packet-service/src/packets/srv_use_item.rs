/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::Reader;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[derive(Debug)]
pub struct SrvUseItem {
    pub(crate) char_id: u16,
    pub(crate) item_id: u16,
    pub(crate) index: u8,
}

impl PacketPayload for SrvUseItem {}

impl Encode for SrvUseItem {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.char_id.encode(encoder)?;
        self.item_id.encode(encoder)?;
        self.index.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for SrvUseItem {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let char_id = u16::decode(decoder)?;
        let item_id = u16::decode(decoder)?;
        let index = u8::decode(decoder)?;
        Ok(Self {
            char_id,
            item_id,
            index,
        })
    }
}
