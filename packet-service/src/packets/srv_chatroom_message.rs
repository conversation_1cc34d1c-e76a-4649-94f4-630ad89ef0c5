/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::<PERSON>;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[derive(Debug)]
pub struct SrvChatroomMessage {
    pub(crate) target: u16,
    pub(crate) message: NullTerminatedString,
}

impl PacketPayload for SrvChatroomMessage {}

impl Encode for SrvChatroomMessage {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.target.encode(encoder)?;
        self.message.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for SrvChatroomMessage {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let target = u16::decode(decoder)?;
        let message = NullTerminatedString::decode(decoder)?;
        Ok(Self { target, message })
    }
}
