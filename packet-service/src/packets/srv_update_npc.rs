/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::<PERSON>;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[derive(Debug)]
pub struct SrvUpdateNpc {
    pub(crate) id: u16,
    pub(crate) npc_id: i16,
}

impl PacketPayload for SrvUpdateNpc {}

impl Encode for SrvUpdateNpc {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.id.encode(encoder)?;
        self.npc_id.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for SrvUpdateNpc {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let id = u16::decode(decoder)?;
        let npc_id = i16::decode(decoder)?;
        Ok(Self { id, npc_id })
    }
}
