/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::<PERSON>;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[derive(Debug)]
pub struct SrvFairy {
    pub(crate) enabled: i8,
    pub(crate) target: u16,
}

impl PacketPayload for SrvFairy {}

impl Encode for SrvFairy {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.enabled.encode(encoder)?;
        self.target.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for SrvFairy {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let enabled = i8::decode(decoder)?;
        let target = u16::decode(decoder)?;
        Ok(Self { enabled, target })
    }
}
