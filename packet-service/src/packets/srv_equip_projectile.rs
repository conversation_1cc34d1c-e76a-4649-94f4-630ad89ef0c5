/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::<PERSON>;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[derive(Debug, <PERSON><PERSON>, Default)]
pub struct ProjectileData {
    pub(crate) type_: u8,
    pub(crate) id: u16,
}

impl Encode for ProjectileData {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.type_.encode(encoder)?;
        self.id.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for ProjectileData {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let type_ = u8::decode(decoder)?;
        let id = u16::decode(decoder)?;
        Ok(Self { type_, id })
    }
}

#[derive(Debug)]
pub struct SrvEquipProjectile {
    pub(crate) char_id: u16,
    pub(crate) projectile: ProjectileData,
}

impl PacketPayload for SrvEquipProjectile {}

impl Encode for SrvEquipProjectile {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.char_id.encode(encoder)?;
        self.projectile.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for SrvEquipProjectile {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let char_id = u16::decode(decoder)?;
        let projectile = ProjectileData::decode(decoder)?;
        Ok(Self { char_id, projectile })
    }
}
