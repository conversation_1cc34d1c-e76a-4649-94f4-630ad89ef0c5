/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::<PERSON>;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[repr(u8)]
#[derive(Debug, Clone)]
pub(crate) enum ReviveRequest {
    RevivePosition = 1,
    SavePosition = 2,
    StartPosition = 3,
    CurrentPosition = 4,
}

impl Encode for ReviveRequest {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        encoder.writer().write(&[self.clone() as u8]).map_err(Into::into)
    }
}

impl<Context> Decode<Context> for ReviveRequest {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let value = u8::decode(decoder)?;
        match value {
            1 => Ok(ReviveRequest::RevivePosition),
            2 => Ok(ReviveRequest::SavePosition),
            3 => Ok(ReviveRequest::StartPosition),
            4 => Ok(ReviveRequest::CurrentPosition),
            _ => Err(bincode::error::DecodeError::OtherString(format!(
                "Invalid value for ReviveRequest: {}",
                value
            ))),
        }
    }
}

#[derive(Debug)]
pub struct CliReviveReq {
    pub(crate) revive_type: ReviveRequest,
}

impl PacketPayload for CliReviveReq {}

impl Encode for CliReviveReq {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.revive_type.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for CliReviveReq {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let revive_type = ReviveRequest::decode(decoder)?;
        Ok(Self { revive_type })
    }
}
