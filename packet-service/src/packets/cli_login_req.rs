/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::Reader;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[derive(Debug)]
pub struct Password {
    pub(crate) password: String,
}

impl Encode for Password {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        let bytes = self.password.as_bytes();
        let fixed_length = 64;
        if bytes.len() > fixed_length {
            return Err(bincode::error::EncodeError::OtherString(format!(
                "password length exceeds fixed size: {} > {}",
                bytes.len(),
                fixed_length
            )));
        }
        encoder.writer().write(bytes)?;
        encoder.writer().write(&vec![0; fixed_length - bytes.len()])?;
        Ok(())
    }
}

impl<Context> Decode<Context> for Password {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let mut buffer = vec![0u8; 64];
        decoder.reader().read(&mut buffer)?;
        let password = String::from_utf8(buffer)
            .map_err(|e| DecodeError::OtherString(format!("Invalid UTF-8: {}", e)))?
            .trim_end_matches('\0')
            .to_string();
        Ok(Self { password })
    }
}

#[derive(Debug)]
pub struct CliLoginReq {
    pub(crate) password: Password,
    pub(crate) username: NullTerminatedString,
}

impl PacketPayload for CliLoginReq {}

impl Encode for CliLoginReq {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.password.encode(encoder)?;
        self.username.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for CliLoginReq {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let password = Password::decode(decoder)?;
        let username = NullTerminatedString::decode(decoder)?;
        Ok(Self { password, username })
    }
}
