/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::<PERSON>;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[derive(Debug)]
pub struct CliChangeMapReq {
    pub(crate) weight_rate: u8,
    pub(crate) z: u16,
}

impl PacketPayload for CliChangeMapReq {}

impl Encode for CliChangeMapReq {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.weight_rate.encode(encoder)?;
        self.z.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for CliChangeMapReq {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let weight_rate = u8::decode(decoder)?;
        let z = u16::decode(decoder)?;
        Ok(Self { weight_rate, z })
    }
}
