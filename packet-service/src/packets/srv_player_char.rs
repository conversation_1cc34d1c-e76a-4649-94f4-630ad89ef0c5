/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::Reader;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[derive(Debug, <PERSON>lone, Default)]
pub struct EquippedItem {
    pub(crate) id: u16,
    pub(crate) gem_opt: u16,
    pub(crate) socket: i8,
    pub(crate) grade: u8,
}

impl Encode for EquippedItem {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.id.encode(encoder)?;
        self.gem_opt.encode(encoder)?;
        self.socket.encode(encoder)?;
        self.grade.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for EquippedItem {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let id = u16::decode(decoder)?;
        let gem_opt = u16::decode(decoder)?;
        let socket = i8::decode(decoder)?;
        let grade = u8::decode(decoder)?;
        Ok(Self {
            id,
            gem_opt,
            socket,
            grade,
        })
    }
}

#[derive(Debug, Clone, Default)]
pub struct Header {
    pub(crate) type_: u8,
    pub(crate) id: u16,
    pub(crate) is_created: u8,
}

impl Encode for Header {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.type_.encode(encoder)?;
        self.id.encode(encoder)?;
        self.is_created.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for Header {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let type_ = u8::decode(decoder)?;
        let id = u16::decode(decoder)?;
        let is_created = u8::decode(decoder)?;
        Ok(Self { type_, id, is_created })
    }
}

#[derive(Debug)]
pub struct SrvPlayerChar {
    pub(crate) id: u16,
    pub(crate) x: f32,
    pub(crate) y: f32,
    pub(crate) dest_x: f32,
    pub(crate) dest_y: f32,
    pub(crate) command: u16,
    pub(crate) target_id: u16,
    pub(crate) move_mode: u8,
    pub(crate) hp: i32,
    pub(crate) team_id: i32,
    pub(crate) status_flag: u32,
    pub(crate) race: u8,
    pub(crate) run_speed: i16,
    pub(crate) atk_speed: i16,
    pub(crate) weight_rate: u8,
    pub(crate) face: u32,
    pub(crate) hair: u32,
    pub(crate) inventory: [EquippedItem; (MAX_VISIBLE_ITEMS as usize)],
    pub(crate) bullets: [Header; (BulletType::MaxBulletTypes as usize)],
    pub(crate) job: i16,
    pub(crate) level: u8,
    pub(crate) riding_items: [EquippedItem; (RidingItem::MaxRidingItems as usize)],
    pub(crate) z: i16,
    pub(crate) sub_flag: u32,
    pub(crate) name: NullTerminatedString,
    pub(crate) other_name: NullTerminatedString,
}

impl PacketPayload for SrvPlayerChar {}

impl Encode for SrvPlayerChar {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.id.encode(encoder)?;
        self.x.encode(encoder)?;
        self.y.encode(encoder)?;
        self.dest_x.encode(encoder)?;
        self.dest_y.encode(encoder)?;
        self.command.encode(encoder)?;
        self.target_id.encode(encoder)?;
        self.move_mode.encode(encoder)?;
        self.hp.encode(encoder)?;
        self.team_id.encode(encoder)?;
        self.status_flag.encode(encoder)?;
        self.race.encode(encoder)?;
        self.run_speed.encode(encoder)?;
        self.atk_speed.encode(encoder)?;
        self.weight_rate.encode(encoder)?;
        self.face.encode(encoder)?;
        self.hair.encode(encoder)?;
        for value in &self.inventory {
            value.encode(encoder)?;
        }
        for value in &self.bullets {
            value.encode(encoder)?;
        }
        self.job.encode(encoder)?;
        self.level.encode(encoder)?;
        for value in &self.riding_items {
            value.encode(encoder)?;
        }
        self.z.encode(encoder)?;
        self.sub_flag.encode(encoder)?;
        self.name.encode(encoder)?;
        self.other_name.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for SrvPlayerChar {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let id = u16::decode(decoder)?;
        let x = f32::decode(decoder)?;
        let y = f32::decode(decoder)?;
        let dest_x = f32::decode(decoder)?;
        let dest_y = f32::decode(decoder)?;
        let command = u16::decode(decoder)?;
        let target_id = u16::decode(decoder)?;
        let move_mode = u8::decode(decoder)?;
        let hp = i32::decode(decoder)?;
        let team_id = i32::decode(decoder)?;
        let status_flag = u32::decode(decoder)?;
        let race = u8::decode(decoder)?;
        let run_speed = i16::decode(decoder)?;
        let atk_speed = i16::decode(decoder)?;
        let weight_rate = u8::decode(decoder)?;
        let face = u32::decode(decoder)?;
        let hair = u32::decode(decoder)?;
        let mut inventory: [EquippedItem; (MAX_VISIBLE_ITEMS as usize)] =
            core::array::from_fn(|i| EquippedItem::default());
        for index in 0..MAX_VISIBLE_ITEMS as usize {
            inventory[index] = EquippedItem::decode(decoder)?;
        }
        let mut bullets: [Header; (BulletType::MaxBulletTypes as usize)] = core::array::from_fn(|i| Header::default());
        for index in 0..BulletType::MaxBulletTypes as usize {
            bullets[index] = Header::decode(decoder)?;
        }
        let job = i16::decode(decoder)?;
        let level = u8::decode(decoder)?;
        let mut riding_items: [EquippedItem; (RidingItem::MaxRidingItems as usize)] =
            core::array::from_fn(|i| EquippedItem::default());
        for index in 0..RidingItem::MaxRidingItems as usize {
            riding_items[index] = EquippedItem::decode(decoder)?;
        }
        let z = i16::decode(decoder)?;
        let sub_flag = u32::decode(decoder)?;
        let name = NullTerminatedString::decode(decoder)?;
        let other_name = NullTerminatedString::decode(decoder)?;
        Ok(Self {
            id,
            x,
            y,
            dest_x,
            dest_y,
            command,
            target_id,
            move_mode,
            hp,
            team_id,
            status_flag,
            race,
            run_speed,
            atk_speed,
            weight_rate,
            face,
            hair,
            inventory,
            bullets,
            job,
            level,
            riding_items,
            z,
            sub_flag,
            name,
            other_name,
        })
    }
}
