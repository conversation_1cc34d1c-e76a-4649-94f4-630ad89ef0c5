/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::Reader;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[repr(u8)]
#[derive(Debug, Clone)]
pub(crate) enum Stat {
    Str = 0,
    Dex = 1,
    Int = 2,
    Con = 3,
    Cha = 4,
    Sen = 5,
}

impl Encode for Stat {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        encoder.writer().write(&[self.clone() as u8]).map_err(Into::into)
    }
}

impl<Context> Decode<Context> for Stat {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let value = u8::decode(decoder)?;
        match value {
            0 => Ok(Stat::Str),
            1 => Ok(Stat::Dex),
            2 => Ok(Stat::Int),
            3 => Ok(Stat::Con),
            4 => Ok(Stat::Cha),
            5 => Ok(Stat::Sen),
            _ => Err(bincode::error::DecodeError::OtherString(format!(
                "Invalid value for Stat: {}",
                value
            ))),
        }
    }
}

#[derive(Debug)]
pub struct CliStatAddReq {
    pub(crate) stat: Stat,
}

impl PacketPayload for CliStatAddReq {}

impl Encode for CliStatAddReq {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.stat.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for CliStatAddReq {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let stat = Stat::decode(decoder)?;
        Ok(Self { stat })
    }
}
