/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::Reader;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[repr(u8)]
#[derive(Debug, Clone)]
pub(crate) enum SkillCancelResult {
    InvalidSkill = 0,
    InvalidTarget = 1,
    TargetNotFound = 2,
}

impl Encode for SkillCancelResult {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        encoder.writer().write(&[self.clone() as u8]).map_err(Into::into)
    }
}

impl<Context> Decode<Context> for SkillCancelResult {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let value = u8::decode(decoder)?;
        match value {
            0 => Ok(SkillCancelResult::InvalidSkill),
            1 => Ok(SkillCancelResult::InvalidTarget),
            2 => Ok(SkillCancelResult::TargetNotFound),
            _ => Err(bincode::error::DecodeError::OtherString(format!(
                "Invalid value for SkillCancelResult: {}",
                value
            ))),
        }
    }
}

#[derive(Debug)]
pub struct SrvSkillCancel {
    pub(crate) target: u16,
    pub(crate) result: SkillCancelResult,
}

impl PacketPayload for SrvSkillCancel {}

impl Encode for SrvSkillCancel {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.target.encode(encoder)?;
        self.result.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for SrvSkillCancel {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let target = u16::decode(decoder)?;
        let result = SkillCancelResult::decode(decoder)?;
        Ok(Self { target, result })
    }
}
