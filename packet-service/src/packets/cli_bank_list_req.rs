/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::Reader;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[repr(u8)]
#[derive(Debug, Clone)]
pub(crate) enum BankListRequest {
    Open = 0,
    ChangePassword = 17,
}

impl Encode for BankListRequest {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        encoder.writer().write(&[self.clone() as u8]).map_err(Into::into)
    }
}

impl<Context> Decode<Context> for BankListRequest {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let value = u8::decode(decoder)?;
        match value {
            0 => Ok(BankListRequest::Open),
            17 => Ok(BankListRequest::ChangePassword),
            _ => Err(bincode::error::DecodeError::OtherString(format!(
                "Invalid value for BankListRequest: {}",
                value
            ))),
        }
    }
}

#[derive(Debug)]
pub struct CliBankListReq {
    pub(crate) request: BankListRequest,
    pub(crate) password: NullTerminatedString,
}

impl PacketPayload for CliBankListReq {}

impl Encode for CliBankListReq {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.request.encode(encoder)?;
        self.password.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for CliBankListReq {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let request = BankListRequest::decode(decoder)?;
        let password = NullTerminatedString::decode(decoder)?;
        Ok(Self { request, password })
    }
}
