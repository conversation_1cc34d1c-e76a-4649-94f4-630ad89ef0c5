/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::<PERSON>;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[derive(Debug)]
pub struct CliEquipItem {
    pub(crate) slot_to: i16,
    pub(crate) slot_from: i16,
}

impl PacketPayload for CliEquipItem {}

impl Encode for CliEquipItem {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.slot_to.encode(encoder)?;
        self.slot_from.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for CliEquipItem {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let slot_to = i16::decode(decoder)?;
        let slot_from = i16::decode(decoder)?;
        Ok(Self { slot_to, slot_from })
    }
}
