/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::Reader;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[repr(u8)]
#[derive(Debug, Clone)]
pub(crate) enum ToggleMove {
    Run = 0,
    Sit = 1,
    Drive = 2,
}

impl Encode for ToggleMove {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        encoder.writer().write(&[self.clone() as u8]).map_err(Into::into)
    }
}

impl<Context> Decode<Context> for ToggleMove {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let value = u8::decode(decoder)?;
        match value {
            0 => Ok(ToggleMove::Run),
            1 => Ok(ToggleMove::Sit),
            2 => Ok(ToggleMove::Drive),
            _ => Err(bincode::error::DecodeError::OtherString(format!(
                "Invalid value for ToggleMove: {}",
                value
            ))),
        }
    }
}

#[derive(Debug)]
pub struct SrvToggleMove {
    pub(crate) object_id: u16,
    pub(crate) type_: ToggleMove,
    pub(crate) run_speed: u16,
}

impl PacketPayload for SrvToggleMove {}

impl Encode for SrvToggleMove {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.object_id.encode(encoder)?;
        self.type_.encode(encoder)?;
        self.run_speed.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for SrvToggleMove {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let object_id = u16::decode(decoder)?;
        let type_ = ToggleMove::decode(decoder)?;
        let run_speed = u16::decode(decoder)?;
        Ok(Self {
            object_id,
            type_,
            run_speed,
        })
    }
}
