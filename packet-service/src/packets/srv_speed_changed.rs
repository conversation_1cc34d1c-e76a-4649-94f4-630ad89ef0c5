/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::<PERSON>;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[derive(Debug)]
pub struct SrvSpeedChanged {
    pub(crate) target: u16,
    pub(crate) move_speed: i16,
    pub(crate) attack_speed: i16,
    pub(crate) weight: u8,
}

impl PacketPayload for SrvSpeedChanged {}

impl Encode for SrvSpeedChanged {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.target.encode(encoder)?;
        self.move_speed.encode(encoder)?;
        self.attack_speed.encode(encoder)?;
        self.weight.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for SrvSpeedChanged {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let target = u16::decode(decoder)?;
        let move_speed = i16::decode(decoder)?;
        let attack_speed = i16::decode(decoder)?;
        let weight = u8::decode(decoder)?;
        Ok(Self {
            target,
            move_speed,
            attack_speed,
            weight,
        })
    }
}
