/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::<PERSON>;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[derive(Debug)]
pub struct SrvSetServerVarReply {
    pub(crate) type_: u8,
    pub(crate) value: u32,
}

impl PacketPayload for SrvSetServerVarReply {}

impl Encode for SrvSetServerVarReply {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.type_.encode(encoder)?;
        self.value.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for SrvSetServerVarReply {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let type_ = u8::decode(decoder)?;
        let value = u32::decode(decoder)?;
        Ok(Self { type_, value })
    }
}
