/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::<PERSON>;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[derive(Debug)]
pub struct CliRepairNpc {
    pub(crate) npc_id: u16,
    pub(crate) target_index: u16,
}

impl PacketPayload for CliRepairNpc {}

impl Encode for CliRepairNpc {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.npc_id.encode(encoder)?;
        self.target_index.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for CliRepairNpc {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let npc_id = u16::decode(decoder)?;
        let target_index = u16::decode(decoder)?;
        Ok(Self { npc_id, target_index })
    }
}
