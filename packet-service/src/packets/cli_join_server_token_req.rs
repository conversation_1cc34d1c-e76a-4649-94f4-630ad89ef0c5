/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::<PERSON>;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[derive(Debug)]
pub struct CliJoinServerTokenReq {
    pub(crate) session_id: u32,
    pub(crate) token: NullTerminatedString,
}

impl PacketPayload for CliJoinServerTokenReq {}

impl Encode for CliJoinServerTokenReq {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.session_id.encode(encoder)?;
        self.token.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for CliJoinServerTokenReq {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let session_id = u32::decode(decoder)?;
        let token = NullTerminatedString::decode(decoder)?;
        Ok(Self { session_id, token })
    }
}
