/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::Reader;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[derive(Debug, <PERSON>lone, Default)]
pub struct EquippedItem {
    pub(crate) id: u16,
    pub(crate) gem_opt: u16,
    pub(crate) socket: i8,
    pub(crate) grade: u8,
}

impl Encode for EquippedItem {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.id.encode(encoder)?;
        self.gem_opt.encode(encoder)?;
        self.socket.encode(encoder)?;
        self.grade.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for EquippedItem {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let id = u16::decode(decoder)?;
        let gem_opt = u16::decode(decoder)?;
        let socket = i8::decode(decoder)?;
        let grade = u8::decode(decoder)?;
        Ok(Self {
            id,
            gem_opt,
            socket,
            grade,
        })
    }
}

#[derive(Debug)]
pub struct SrvSelectCharReply {
    pub(crate) race: u8,
    pub(crate) map: u16,
    pub(crate) x: f32,
    pub(crate) y: f32,
    pub(crate) spawn: u16,
    // It's actually the face that is expected here
    pub(crate) body_face: u32,
    // It's actually the hair that is expected here
    pub(crate) body_hair: u32,
    pub(crate) equipped_items: [EquippedItem; (MAX_VISIBLE_ITEMS as usize)],
    pub(crate) stone: u8,
    pub(crate) face: u8,
    pub(crate) hair: u8,
    pub(crate) job: u16,
    pub(crate) faction_id: u8,
    pub(crate) faction_rank: u8,
    pub(crate) fame: u8,
    pub(crate) str: u16,
    pub(crate) dex: u16,
    pub(crate) int: u16,
    pub(crate) con: u16,
    pub(crate) charm: u16,
    pub(crate) sense: u16,
    pub(crate) hp: i32,
    pub(crate) mp: i32,
    pub(crate) xp: u32,
    pub(crate) level: u16,
    pub(crate) stat_points: u32,
    pub(crate) skill_points: u32,
    pub(crate) body_size: u8,
    pub(crate) head_size: u8,
    pub(crate) penalty_xp: u32,
    pub(crate) faction_fame: [u16; 2],
    pub(crate) faction_points: [u16; 10],
    pub(crate) guild_id: u32,
    pub(crate) guild_contribution: u16,
    pub(crate) guild_rank: u8,
    pub(crate) pk_flag: u16,
    pub(crate) stamina: u16,
    pub(crate) effects: [StatusEffect; (MAX_STATUS_EFFECTS as usize)],
    pub(crate) pat_hp: u16,
    pub(crate) pat_cooldown_time: u32,
    pub(crate) skills: [u16; (MAX_SKILL_COUNT as usize)],
    pub(crate) hotbar: [HotbarItem; (MAX_HOTBAR_ITEMS as usize)],
    pub(crate) tag: u32,
    pub(crate) name: NullTerminatedString,
}

impl PacketPayload for SrvSelectCharReply {}

impl Encode for SrvSelectCharReply {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.race.encode(encoder)?;
        self.map.encode(encoder)?;
        self.x.encode(encoder)?;
        self.y.encode(encoder)?;
        self.spawn.encode(encoder)?;
        self.body_face.encode(encoder)?;
        self.body_hair.encode(encoder)?;
        for value in &self.equipped_items {
            value.encode(encoder)?;
        }
        self.stone.encode(encoder)?;
        self.face.encode(encoder)?;
        self.hair.encode(encoder)?;
        self.job.encode(encoder)?;
        self.faction_id.encode(encoder)?;
        self.faction_rank.encode(encoder)?;
        self.fame.encode(encoder)?;
        self.str.encode(encoder)?;
        self.dex.encode(encoder)?;
        self.int.encode(encoder)?;
        self.con.encode(encoder)?;
        self.charm.encode(encoder)?;
        self.sense.encode(encoder)?;
        self.hp.encode(encoder)?;
        self.mp.encode(encoder)?;
        self.xp.encode(encoder)?;
        self.level.encode(encoder)?;
        self.stat_points.encode(encoder)?;
        self.skill_points.encode(encoder)?;
        self.body_size.encode(encoder)?;
        self.head_size.encode(encoder)?;
        self.penalty_xp.encode(encoder)?;
        for value in &self.faction_fame {
            value.encode(encoder)?;
        }
        for value in &self.faction_points {
            value.encode(encoder)?;
        }
        self.guild_id.encode(encoder)?;
        self.guild_contribution.encode(encoder)?;
        self.guild_rank.encode(encoder)?;
        self.pk_flag.encode(encoder)?;
        self.stamina.encode(encoder)?;
        for value in &self.effects {
            value.encode(encoder)?;
        }
        self.pat_hp.encode(encoder)?;
        self.pat_cooldown_time.encode(encoder)?;
        for value in &self.skills {
            value.encode(encoder)?;
        }
        for value in &self.hotbar {
            value.encode(encoder)?;
        }
        self.tag.encode(encoder)?;
        self.name.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for SrvSelectCharReply {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let race = u8::decode(decoder)?;
        let map = u16::decode(decoder)?;
        let x = f32::decode(decoder)?;
        let y = f32::decode(decoder)?;
        let spawn = u16::decode(decoder)?;
        let body_face = u32::decode(decoder)?;
        let body_hair = u32::decode(decoder)?;
        let mut equipped_items: [EquippedItem; (MAX_VISIBLE_ITEMS as usize)] =
            core::array::from_fn(|i| EquippedItem::default());
        for index in 0..MAX_VISIBLE_ITEMS as usize {
            equipped_items[index] = EquippedItem::decode(decoder)?;
        }
        let stone = u8::decode(decoder)?;
        let face = u8::decode(decoder)?;
        let hair = u8::decode(decoder)?;
        let job = u16::decode(decoder)?;
        let faction_id = u8::decode(decoder)?;
        let faction_rank = u8::decode(decoder)?;
        let fame = u8::decode(decoder)?;
        let str = u16::decode(decoder)?;
        let dex = u16::decode(decoder)?;
        let int = u16::decode(decoder)?;
        let con = u16::decode(decoder)?;
        let charm = u16::decode(decoder)?;
        let sense = u16::decode(decoder)?;
        let hp = i32::decode(decoder)?;
        let mp = i32::decode(decoder)?;
        let xp = u32::decode(decoder)?;
        let level = u16::decode(decoder)?;
        let stat_points = u32::decode(decoder)?;
        let skill_points = u32::decode(decoder)?;
        let body_size = u8::decode(decoder)?;
        let head_size = u8::decode(decoder)?;
        let penalty_xp = u32::decode(decoder)?;
        let mut faction_fame = [0u16; 2];
        for value in &mut faction_fame {
            *value = u16::decode(decoder)?;
        }
        let mut faction_points = [0u16; 10];
        for value in &mut faction_points {
            *value = u16::decode(decoder)?;
        }
        let guild_id = u32::decode(decoder)?;
        let guild_contribution = u16::decode(decoder)?;
        let guild_rank = u8::decode(decoder)?;
        let pk_flag = u16::decode(decoder)?;
        let stamina = u16::decode(decoder)?;
        let mut effects: [StatusEffect; (MAX_STATUS_EFFECTS as usize)] =
            core::array::from_fn(|i| StatusEffect::default());
        for index in 0..MAX_STATUS_EFFECTS as usize {
            effects[index] = StatusEffect::decode(decoder)?;
        }
        let pat_hp = u16::decode(decoder)?;
        let pat_cooldown_time = u32::decode(decoder)?;
        let mut skills = [0u16; (MAX_SKILL_COUNT as usize)];
        for value in &mut skills {
            *value = u16::decode(decoder)?;
        }
        let mut hotbar: [HotbarItem; (MAX_HOTBAR_ITEMS as usize)] = core::array::from_fn(|i| HotbarItem::default());
        for index in 0..MAX_HOTBAR_ITEMS as usize {
            hotbar[index] = HotbarItem::decode(decoder)?;
        }
        let tag = u32::decode(decoder)?;
        let name = NullTerminatedString::decode(decoder)?;
        Ok(Self {
            race,
            map,
            x,
            y,
            spawn,
            body_face,
            body_hair,
            equipped_items,
            stone,
            face,
            hair,
            job,
            faction_id,
            faction_rank,
            fame,
            str,
            dex,
            int,
            con,
            charm,
            sense,
            hp,
            mp,
            xp,
            level,
            stat_points,
            skill_points,
            body_size,
            head_size,
            penalty_xp,
            faction_fame,
            faction_points,
            guild_id,
            guild_contribution,
            guild_rank,
            pk_flag,
            stamina,
            effects,
            pat_hp,
            pat_cooldown_time,
            skills,
            hotbar,
            tag,
            name,
        })
    }
}
