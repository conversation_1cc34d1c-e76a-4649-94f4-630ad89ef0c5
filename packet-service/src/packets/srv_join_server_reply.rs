/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::Reader;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[repr(u8)]
#[derive(Debug, Clone)]
pub(crate) enum Result {
    Ok = 0,
    Failed = 1,
    TimeOut = 2,
    InvalidPassword = 3,
    AlreadyLoggedin = 4,
}

impl Encode for Result {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        encoder.writer().write(&[self.clone() as u8]).map_err(Into::into)
    }
}

impl<Context> Decode<Context> for Result {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let value = u8::decode(decoder)?;
        match value {
            0 => Ok(Result::Ok),
            1 => Ok(Result::Failed),
            2 => Ok(Result::TimeOut),
            3 => Ok(Result::InvalidPassword),
            4 => Ok(Result::AlreadyLoggedin),
            _ => Err(bincode::error::DecodeError::OtherString(format!(
                "Invalid value for Result: {}",
                value
            ))),
        }
    }
}

#[derive(Debug)]
pub struct SrvJoinServerReply {
    pub(crate) result: Result,
    pub(crate) id: u32,
    pub(crate) pay_flag: u32,
}

impl PacketPayload for SrvJoinServerReply {}

impl Encode for SrvJoinServerReply {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.result.encode(encoder)?;
        self.id.encode(encoder)?;
        self.pay_flag.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for SrvJoinServerReply {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let result = Result::decode(decoder)?;
        let id = u32::decode(decoder)?;
        let pay_flag = u32::decode(decoder)?;
        Ok(Self { result, id, pay_flag })
    }
}
