/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::Reader;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[derive(Debug)]
pub struct CliAttack {
    pub(crate) target_id: u16,
}

impl PacketPayload for CliAttack {}

impl Encode for CliAttack {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.target_id.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for CliAttack {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let target_id = u16::decode(decoder)?;
        Ok(Self { target_id })
    }
}
