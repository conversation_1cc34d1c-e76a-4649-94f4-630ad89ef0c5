/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::Reader;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[derive(Debug)]
pub struct CliCraftReq {
    pub(crate) slot: u8,
    pub(crate) type_: i8,
    pub(crate) id: i8,
    pub(crate) material_slots: [i16; 4],
}

impl PacketPayload for CliCraftReq {}

impl Encode for CliCraftReq {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.slot.encode(encoder)?;
        self.type_.encode(encoder)?;
        self.id.encode(encoder)?;
        for value in &self.material_slots {
            value.encode(encoder)?;
        }
        Ok(())
    }
}

impl<Context> Decode<Context> for CliCraftReq {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let slot = u8::decode(decoder)?;
        let type_ = i8::decode(decoder)?;
        let id = i8::decode(decoder)?;
        let mut material_slots = [0i16; 4];
        for value in &mut material_slots {
            *value = i16::decode(decoder)?;
        }
        Ok(Self {
            slot,
            type_,
            id,
            material_slots,
        })
    }
}
