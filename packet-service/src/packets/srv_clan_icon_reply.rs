/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::Reader;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[repr(u16)]
#[derive(Debug, Clone)]
pub(crate) enum IconError {
    TooManyUpdates = 0,
    DbError = 1,
    SpError = 2,
}

impl Encode for IconError {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        encoder.writer().write(&[self.clone() as u8]).map_err(Into::into)
    }
}

impl<Context> Decode<Context> for IconError {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let value = u16::decode(decoder)?;
        match value {
            0 => Ok(IconError::TooManyUpdates),
            1 => Ok(IconError::DbError),
            2 => Ok(IconError::SpError),
            _ => Err(bincode::error::DecodeError::OtherString(format!(
                "Invalid value for IconError: {}",
                value
            ))),
        }
    }
}

#[derive(Debug)]
pub struct SrvClanIconReply {
    pub(crate) id: u16,
    pub(crate) data: Vec<u8>,
}

impl PacketPayload for SrvClanIconReply {}

impl Encode for SrvClanIconReply {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.id.encode(encoder)?;
        self.data.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for SrvClanIconReply {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let id = u16::decode(decoder)?;
        let data = Vec::decode(decoder)?;
        Ok(Self { id, data })
    }
}
