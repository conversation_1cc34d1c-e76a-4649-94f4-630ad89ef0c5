/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::<PERSON>;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[derive(Debug)]
pub struct CliClanIconSet {
    pub(crate) crc: u16,
    pub(crate) data: Vec<u8>,
}

impl PacketPayload for CliClanIconSet {}

impl Encode for CliClanIconSet {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.crc.encode(encoder)?;
        self.data.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for CliClanIconSet {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let crc = u16::decode(decoder)?;
        let data = Vec::decode(decoder)?;
        Ok(Self { crc, data })
    }
}
