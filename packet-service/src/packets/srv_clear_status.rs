/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::Reader;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[derive(Debug)]
pub struct SrvClearStatus {
    pub(crate) target: u16,
    pub(crate) status: u32,
    pub(crate) hp: i16,
    pub(crate) mp: i16,
}

impl PacketPayload for SrvClearStatus {}

impl Encode for SrvClearStatus {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.target.encode(encoder)?;
        self.status.encode(encoder)?;
        self.hp.encode(encoder)?;
        self.mp.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for SrvClearStatus {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let target = u16::decode(decoder)?;
        let status = u32::decode(decoder)?;
        let hp = i16::decode(decoder)?;
        let mp = i16::decode(decoder)?;
        Ok(Self { target, status, hp, mp })
    }
}
