/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::<PERSON>;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[derive(Debug)]
pub struct CliScreenShotTimeReq {
    pub(crate) count: u16,
}

impl PacketPayload for CliScreenShotTimeReq {}

impl Encode for CliScreenShotTimeReq {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.count.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for CliScreenShotTimeReq {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let count = u16::decode(decoder)?;
        Ok(Self { count })
    }
}
