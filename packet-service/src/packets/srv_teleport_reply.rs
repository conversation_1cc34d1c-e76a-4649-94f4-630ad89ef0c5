/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::<PERSON>;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[derive(Debug)]
pub struct SrvTeleportReply {
    pub(crate) id: u16,
    pub(crate) map: u16,
    pub(crate) x: f32,
    pub(crate) y: f32,
    pub(crate) move_mode: u8,
    pub(crate) ride_mode: u8,
}

impl PacketPayload for SrvTeleportReply {}

impl Encode for SrvTeleportReply {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.id.encode(encoder)?;
        self.map.encode(encoder)?;
        self.x.encode(encoder)?;
        self.y.encode(encoder)?;
        self.move_mode.encode(encoder)?;
        self.ride_mode.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for SrvTeleportReply {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let id = u16::decode(decoder)?;
        let map = u16::decode(decoder)?;
        let x = f32::decode(decoder)?;
        let y = f32::decode(decoder)?;
        let move_mode = u8::decode(decoder)?;
        let ride_mode = u8::decode(decoder)?;
        Ok(Self {
            id,
            map,
            x,
            y,
            move_mode,
            ride_mode,
        })
    }
}
