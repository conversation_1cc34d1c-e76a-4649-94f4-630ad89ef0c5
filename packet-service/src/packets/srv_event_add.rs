/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::Reader;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[derive(Debug)]
pub struct SrvEventAdd {
    pub(crate) id: u16,
    pub(crate) x: i8,
    pub(crate) y: i8,
    pub(crate) event_id: u16,
    pub(crate) status: i16,
}

impl PacketPayload for SrvEventAdd {}

impl Encode for SrvEventAdd {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.id.encode(encoder)?;
        self.x.encode(encoder)?;
        self.y.encode(encoder)?;
        self.event_id.encode(encoder)?;
        self.status.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for SrvEventAdd {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let id = u16::decode(decoder)?;
        let x = i8::decode(decoder)?;
        let y = i8::decode(decoder)?;
        let event_id = u16::decode(decoder)?;
        let status = i16::decode(decoder)?;
        Ok(Self {
            id,
            x,
            y,
            event_id,
            status,
        })
    }
}
