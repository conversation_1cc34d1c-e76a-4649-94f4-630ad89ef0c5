/* This file is @generated with IDL v0.2.2 */

use crate::dataconsts::*;
use crate::enums::*;
use crate::packet::PacketPayload;
use crate::types::*;
use bincode::de::read::Reader;
use bincode::enc::write::Writer;
use bincode::{de::Decoder, enc::Encoder, error::DecodeError, Decode, Encode};
use utils::null_string::NullTerminatedString;

#[repr(u8)]
#[derive(Debug, Clone)]
pub(crate) enum MobAnimation {
    Stop = 0,
    Attack = 1,
    Hit = 2,
    Die = 3,
    Run = 4,
    Action1 = 5,
    Skill1 = 6,
    Action2 = 7,
    Skill2 = 8,
    Etc = 9,
}

impl Encode for MobAnimation {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        encoder.writer().write(&[self.clone() as u8]).map_err(Into::into)
    }
}

impl<Context> Decode<Context> for MobAnimation {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let value = u8::decode(decoder)?;
        match value {
            0 => Ok(MobAnimation::Stop),
            1 => Ok(MobAnimation::Attack),
            2 => Ok(MobAnimation::Hit),
            3 => Ok(MobAnimation::Die),
            4 => Ok(MobAnimation::Run),
            5 => Ok(MobAnimation::Action1),
            6 => Ok(MobAnimation::Skill1),
            7 => Ok(MobAnimation::Action2),
            8 => Ok(MobAnimation::Skill2),
            9 => Ok(MobAnimation::Etc),
            _ => Err(bincode::error::DecodeError::OtherString(format!(
                "Invalid value for MobAnimation: {}",
                value
            ))),
        }
    }
}

#[derive(Debug)]
pub struct SrvSkillCastSelf {
    pub(crate) target: u16,
    pub(crate) id: i8,
    pub(crate) animation: MobAnimation,
}

impl PacketPayload for SrvSkillCastSelf {}

impl Encode for SrvSkillCastSelf {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> std::result::Result<(), bincode::error::EncodeError> {
        self.target.encode(encoder)?;
        self.id.encode(encoder)?;
        self.animation.encode(encoder)?;
        Ok(())
    }
}

impl<Context> Decode<Context> for SrvSkillCastSelf {
    fn decode<D: Decoder>(decoder: &mut D) -> std::result::Result<Self, bincode::error::DecodeError> {
        let target = u16::decode(decoder)?;
        let id = i8::decode(decoder)?;
        let animation = MobAnimation::decode(decoder)?;
        Ok(Self { target, id, animation })
    }
}
