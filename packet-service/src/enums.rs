#[repr(u8)]
#[derive(Debug, <PERSON><PERSON>, Co<PERSON>, PartialEq, Eq)]
pub(crate) enum BulletType {
    Arrow = 0,
    Bullet = 1,
    Throw = 2,
    MaxBulletTypes,
}

#[repr(u8)]
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub(crate) enum RidingItem {
    Body = 0,
    Engine = 1,
    Legs,
    Option, // weapon or back seat
    Arms,
    MaxRidingItems,
}

#[repr(u16)]
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub(crate) enum ItemSubType {
    Rring = 171,
    Necklace,
    Earring,
    OneHSword = 211,
    OneHBlunt,
    TwoHSword = 221,
    Spear = 222,
    TwoHAxe = 223,
    Bow = 231,
    <PERSON>,
    Launcher,
    Staff = 241,
    Wand,
    Katar = 251,
    DualWield,
    Xbow = 271,
}

#[repr(u8)]
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub(crate) enum EquippedPosition {
    Goggles = 1,
    Helmet = 2,
    <PERSON><PERSON>,
    Backpack,
    Gaunt<PERSON>,
    <PERSON>,
    WeaponR,
    <PERSON>apon<PERSON>,
    <PERSON>lace,
    Ring,
    Earing,
    MaxEquipItems,
}
impl EquippedPosition {
    pub(crate) fn from_i32(value: i32) -> Option<EquippedPosition> {
        match value {
            1 => Some(EquippedPosition::Goggles),
            2 => Some(EquippedPosition::Helmet),
            3 => Some(EquippedPosition::Armor),
            4 => Some(EquippedPosition::Backpack),
            5 => Some(EquippedPosition::Gauntlet),
            6 => Some(EquippedPosition::Boots),
            7 => Some(EquippedPosition::WeaponR),
            8 => Some(EquippedPosition::WeaponL),
            9 => Some(EquippedPosition::Necklace),
            10 => Some(EquippedPosition::Ring),
            11 => Some(EquippedPosition::Earing),
            _ => None,
        }
    }
}

#[repr(u8)]
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub(crate) enum MoveMode {
    Walk = 0,
    Run = 1,
    Drive = 2,
    RideOn = 4,
}

#[repr(u16)]
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub(crate) enum Command {
    Stop = 0,
    Move = 1,
    Attack = 2,
    Die = 3,
    Pickup = 4,
    Skill2Self = 6,
    Skill2Obj = 7,
    Skill2Pos = 8,
    Runaway = 0x8009,
    Sit = 10,
}

#[repr(u8)]
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub(crate) enum ItemType {
    None = 0,
    ItemGoggles = 1,
    ItemHelmet = 2,
    ItemArmor = 3,
    ItemGauntlet = 4,
    ItemBoots = 5,
    ItemBackpack = 6,
    ItemRing = 7,
    ItemWeaponR = 8,
    ItemWeaponL = 9,
    ItemConsumable = 10,
    ItemEtcGem = 11,
    ItemEtc = 12,
    ItemEtc2 = 13,
    ItemRiding = 14,
    Zuly = 0x1F,
}

impl ItemType {
    pub(crate) fn from_i32(value: i32) -> Option<ItemType> {
        match value {
            1 => Some(ItemType::ItemGoggles),
            2 => Some(ItemType::ItemHelmet),
            3 => Some(ItemType::ItemArmor),
            4 => Some(ItemType::ItemGauntlet),
            5 => Some(ItemType::ItemBoots),
            6 => Some(ItemType::ItemBackpack),
            7 => Some(ItemType::ItemRing),
            8 => Some(ItemType::ItemWeaponR),
            9 => Some(ItemType::ItemWeaponL),
            10 => Some(ItemType::ItemConsumable),
            11 => Some(ItemType::ItemEtcGem),
            12 => Some(ItemType::ItemEtc),
            13 => Some(ItemType::ItemEtc2),
            14 => Some(ItemType::ItemRiding),
            0x1F => Some(ItemType::Zuly),
            _ => None,
        }
    }
}

mod party_req {
    #[repr(u8)]
    #[derive(Debug, Clone, Copy, PartialEq, Eq)]
    pub enum Request {
        Make = 0,
        Join = 1,
        Left,
        ChangeOwner,
        Kick = 0x81,
    }
}
