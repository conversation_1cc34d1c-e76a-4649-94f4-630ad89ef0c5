use lazy_static::lazy_static;
use prometheus::{
    register_counter, register_gauge, register_histogram, Counter, Encoder, Gauge, Histogram, TextEncoder,
};

lazy_static! {
    // Counter to track the number of packets received
    pub static ref PACKETS_RECEIVED: Counter = register_counter!(
        "packets_received_total",
        "Total number of packets received"
    ).unwrap();

    // Counter to track the number of packets sent
    pub static ref PACKETS_SENT: Counter = register_counter!(
        "packets_sent_total",
        "Total number of packets sent"
    ).unwrap();

    // Gauge to track the number of active connections
    pub static ref ACTIVE_CONNECTIONS: Gauge = register_gauge!(
        "active_connections",
        "Number of currently active connections"
    ).unwrap();

    // Histogram to track packet processing latency
    pub static ref PACKET_PROCESSING_TIME: Histogram = register_histogram!(
        "packet_processing_time_seconds",
        "Histogram of packet processing times"
    ).unwrap();
}
