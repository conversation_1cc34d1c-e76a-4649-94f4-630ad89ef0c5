use crate::connection_state::ConnectionState;
use crate::id_manager::IdManager;
use dashmap::DashMap;
use std::sync::{Arc, Mutex};
use uuid::Uuid;
use tokio::net::TcpStream;
use tokio::io::{WriteHalf};

#[derive(<PERSON><PERSON>, Debug)]
pub struct ConnectionService {
    pub connections: Arc<DashMap<String, ConnectionState>>, // Map connection ID to state
    pub id_manager: Arc<Mutex<IdManager>>
}

impl ConnectionService {
    pub fn new() -> Self {
        Self {
            connections: Arc::new(DashMap::new()),
            id_manager: Arc::new(Mutex::new(IdManager::new())),
        }
    }

    pub fn add_connection(&self, writer: Arc<tokio::sync::Mutex<WriteHalf<TcpStream>>>) -> String {
        let connection_id = Uuid::new_v4().to_string();
        let mut connection_state = ConnectionState::new();
        connection_state.writer = Some(writer);
        self.connections.insert(connection_id.clone(), connection_state);
        connection_id
    }

    pub fn get_connection(&self, connection_id: &str) -> Option<ConnectionState> {
        self.connections.get(connection_id).map(|entry| entry.clone())
    }

    pub fn get_connection_mut(
        &self,
        connection_id: &str,
    ) -> Option<dashmap::mapref::one::RefMut<'_, String, ConnectionState>> {
        self.connections.get_mut(connection_id)
    }

    pub fn remove_connection(&self, connection_id: &str) {
        self.connections.remove(connection_id);
    }

    pub fn next_id(&self) -> u16 {
        let mut manager = self.id_manager.lock().unwrap();
        manager.get_free_id()
    }

    pub fn free_id(&self, id: u16) {
        let mut manager = self.id_manager.lock().unwrap();
        manager.release_id(id);
    }
}
