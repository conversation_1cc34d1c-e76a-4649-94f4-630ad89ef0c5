use std::collections::VecDeque;
use std::sync::Arc;
use tokio::sync::{Mute<PERSON>, Semaphore};

const MAX_PACKET_SIZE: usize = 0xFFF;

pub struct BufferPool {
    buffers: Mutex<VecDeque<Vec<u8>>>,
    sem: Semaphore,
}

impl BufferPool {
    pub fn new(pool_size: usize) -> Arc<Self> {
        Arc::new(Self {
            buffers: Mutex::new((0..pool_size).map(|_| vec![0u8; MAX_PACKET_SIZE]).collect()),
            sem: Semaphore::new(pool_size),
        })
    }

    pub async fn acquire(&self) -> Option<Vec<u8>> {
        let _ = self.sem.acquire().await.ok()?;
        let mut buffers = self.buffers.lock().await;
        buffers.pop_front()
    }

    pub async fn release(&self, buffer: Vec<u8>) {
        let mut buffers = self.buffers.lock().await;
        buffers.push_back(buffer);
        self.sem.add_permits(1);
    }
}
