use crate::character::character_service_client::CharacterServiceClient;
use crate::character::{
    CreateCharacterRequest, CreateCharacterResponse, DeleteCharacterRequest, DeleteCharacterResponse,
    GetCharacterListRequest, GetCharacterListResponse, GetCharacterRequest, GetCharacterResponse,
};
use tonic::transport::Channel;
use utils::null_string::NullTerminatedString;

#[derive(<PERSON>lone, Debug)]
pub struct CharacterClient {
    client: CharacterServiceClient<Channel>,
}

impl CharacterClient {
    pub async fn connect(endpoint: &str) -> Result<Self, Box<dyn std::error::Error>> {
        let client = CharacterServiceClient::connect(endpoint.to_string()).await?;
        Ok(CharacterClient { client })
    }

    pub async fn get_character_list(
        &mut self,
        user_id: &str,
    ) -> Result<GetCharacterListResponse, Box<dyn std::error::Error + Send + Sync>> {
        let request = GetCharacterListRequest {
            user_id: user_id.to_string(),
        };

        let response = self.client.get_character_list(request).await?;
        Ok(response.into_inner())
    }

    pub async fn create_character(
        &mut self,
        user_id: &str,
        name: NullTerminatedString,
        race: u8,
        face: u8,
        hair: u8,
        stone: u8,
    ) -> Result<CreateCharacterResponse, Box<dyn std::error::Error + Send + Sync>> {
        let request = CreateCharacterRequest {
            user_id: user_id.to_string(),
            name: name.0,
            race: race as i32,
            face: face as i32,
            hair: hair as i32,
            stone: stone as i32,
        };

        let response = self.client.create_character(request).await?;
        Ok(response.into_inner())
    }

    pub async fn delete_character(
        &mut self,
        user_id: &str,
        char_id: &str,
        delete_type: i32,
    ) -> Result<DeleteCharacterResponse, Box<dyn std::error::Error + Send + Sync>> {
        let request = DeleteCharacterRequest {
            user_id: user_id.to_string(),
            char_id: char_id.to_string(),
            delete_type,
        };

        let response = self.client.delete_character(request).await?;
        Ok(response.into_inner())
    }

    pub async fn get_character(
        &mut self,
        user_id: &str,
        char_id: u8,
    ) -> Result<GetCharacterResponse, Box<dyn std::error::Error + Send + Sync>> {
        let request = GetCharacterRequest {
            user_id: user_id.to_string(),
            char_id: char_id.to_string(),
        };

        let response = self.client.get_character(request).await?;
        Ok(response.into_inner())
    }
}
