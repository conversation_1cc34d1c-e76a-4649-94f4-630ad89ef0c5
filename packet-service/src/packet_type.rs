use std::convert::TryFrom;
use thiserror::Error;

#[repr(u16)]
#[derive(Debug, <PERSON><PERSON>, Co<PERSON>, PartialEq, Eq)]
pub enum PacketType {
    PakcsAlive = 0x0500,
    PakssError = 0x0501,
    PakssAnnounceText = 0x0502,
    PakswAnnounceChat = 0x0503,
    PakcsAcceptReq = 0x0504,
    PakcsChannelListReq = 0x0505,
    PaklcChannelListReply = 0x0506,
    PakcsLogoutReq = 0x0507,
    PakwcLogoutReply = 0x0508,
    PakcsLoginReq = 0x0509,
    PakcsLoginTokenReq = 0x050A,
    PaklcLoginReply = 0x050B,
    PakgcLoginReply = 0x050C,
    PakcsSrvSelectReq = 0x050D,
    PaklcSrvSelectReply = 0x050E,
    PakcsJoinServerReq = 0x050F,
    <PERSON>csJoinServerTokenReq = 0x0510,
    PakscJoinServerReply = 0x0511,
    PakwcGmCommand = 0x0512,
    PakwcGlobalVars = 0x0513,
    PakwcGlobalFlags = 0x0514,
    PakccSwitchServer = 0x0515,
    PakcsCharListReq = 0x0516,
    PakccCharListReply = 0x0517,
    PakcsCreateCharReq = 0x0518,
    PakccCreateCharReply = 0x0519,
    PakcsDeleteCharReq = 0x051A,
    PakccDeleteCharReply = 0x051B,
    PakcsSelectCharReq = 0x051C,
    PakwcSelectCharReply = 0x051D,
    PakwcInventoryData = 0x051E,
    PakwcSetMoneyAndItem = 0x051F,
    PakwcSetItem = 0x0520,
    PakwcServerData = 0x0521,
    PakwcQuestData = 0x0522,
    PakcsChangeCharReq = 0x0523,
    PakccChanCharReply = 0x0524,
    PakwcSetMoney = 0x0525,
    PakwcQuestRewardMoney = 0x0526,
    PakwcQuestRewardItem = 0x0527,
    PakwcQuestRewardAddValue = 0x0528,
    PakwcQuestRewardSetValue = 0x0529,
    PakcsCancelLogout = 0x052A,
    PakwcQuestUpdate = 0x052B,
    PakwcWishList = 0x052C,
    PakcsQuestDataReq = 0x052D,
    PakwcQuestDataReply = 0x052E,
    PakwcNpcEvent = 0x052F,
    PakwcGmCommandCode = 0x0530,
    PakcsChangeMapReq = 0x0531,
    PakwcChangeMapReply = 0x0532,
    PakwcInitData = 0x0533,
    PakcsReviveReq = 0x0534,
    PakwcReviveReply = 0x0535,
    PakcsReviveSetPos = 0x0536,
    PakcsSetServerVarReq = 0x0537,
    PakwcSetServerVarReply = 0x0538,
    PakcsCharInfoReq = 0x0539,
    PakwcCharInfoReply = 0x053A,
    PakcsSetWeightReq = 0x053B,
    PakwcSetWeight = 0x053C,
    PakwcSetPosition = 0x053D,
    PakcsStopMoving = 0x053E,
    PakwcStopMoving = 0x053F,
    PakwcUpdateNpc = 0x0540,
    PakcsSummonCmd = 0x0541,
    PakwcSummonCmd = 0x0542,
    PakcsSetAnimation = 0x0543,
    PacwcSetAnimation = 0x0544,
    PakcsToggleMove = 0x0545,
    PakwcToggleMove = 0x0546,
    PakcsNormalChat = 0x0547,
    PakwcNormalChat = 0x0548,
    PakcsWhisperChat = 0x0549,
    PakwcWhisperChat = 0x054A,
    PakcsShoutChat = 0x054B,
    PakwcShoutChat = 0x054C,
    PakcsPartyChat = 0x054D,
    PakwcPartyChat = 0x054E,
    PakcsClanChat = 0x054F,
    PakwcClanChat = 0x0550,
    PakcsAlliedChat = 0x0551,
    PakwcAlliedChat = 0x0552,
    PakcsAlliedShoutChat = 0x0553,
    PakwcAlliedShoutChat = 0x0554,
    PakwcEventStatus = 0x0555,
    PakwcNpcChar = 0x0556,
    PakwcMobChar = 0x0557,
    PakwcPlayerChar = 0x0558,
    PakwcRemoveObject = 0x0559,
    PakcsSetPosition = 0x055A,
    PakcsStop = 0x055B,
    PakwcStop = 0x055C,
    PakwcMove = 0x055D,
    PakcsAttack = 0x055E,
    PakwcAttack = 0x055F,
    PakcsDamage = 0x0560,
    PakwcDamage = 0x0561,
    PakcsMouseCmd = 0x0562,
    PakwcMouseCmd = 0x0563,
    PakwcSetExp = 0x0564,
    PakwcLevelup = 0x0565,
    PakcsHpReq = 0x0566,
    PakwcHpReply = 0x0567,
    PakwcSetHpAndMp = 0x0568,
    PakcsStoreTradeReq = 0x0569,
    PakwcStoreTradeReply = 0x056A,
    PakcsUseItem = 0x056B,
    PakwcUseItem = 0x056C,
    PakcsDropItem = 0x056D,
    PakcsEquipItem = 0x056E,
    PakwcEquipItem = 0x056F,
    PakwcDropItem = 0x0570,
    PakcsPickupItemReq = 0x0571,
    PakwcPickupItemReply = 0x0572,
    PakcsTeleportReq = 0x0573,
    PakwcTeleportReply = 0x0574,
    PakcsStatAddReq = 0x0575,
    PakwcStatAddReply = 0x0576,
    PakcsHotbarSetIconReq = 0x0577,
    PakwcHotbarSetIconReply = 0x0578,
    PakcsEquipProjectile = 0x0579,
    PakwcEquipProjectile = 0x057A,
    PakwcChangeSkin = 0x057B,
    PakcsBankListReq = 0x057C,
    PakwcBankListReply = 0x057D,
    PakcsBankMoveItem = 0x057E,
    PakwcBankMoveItem = 0x057F,
    PakcsCraftReq = 0x0580,
    PakwcCraftReply = 0x0581,
    PakwcSkillLearn = 0x0582,
    PakcsSkillLevelReq = 0x0583,
    PakwcSkillLevelReply = 0x0584,
    PakcsSkillCastSelf = 0x0585,
    PakwcSkillCastSelf = 0x0586,
    PakcsSkillCastTarget = 0x0587,
    PakwcSkillCastTarget = 0x0588,
    PakcsSkillCastPosition = 0x0589,
    PakwcSkillCastPosition = 0x058A,
    PakwcSkillEffect = 0x058B,
    PakwcSkillDamage = 0x058C,
    PakwcClearStatus = 0x058D,
    PakwcSpeedChanged = 0x058E,
    PakwcSkillFinish = 0x058F,
    PakcsAppraisalReq = 0x0590,
    PakwcAppraisalReply = 0x0591,
    PakwcSkillStart = 0x0592,
    PakcsCraftEnhanceReq = 0x0593,
    PakwcCraftEnhanceReply = 0x0594,
    PakwcSkillCancel = 0x0595,
    PakcsWishlistAdd = 0x0596,
    PakcsTrade = 0x0597,
    PakwcTrade = 0x0598,
    PakcsTradeItem = 0x0599,
    PakwcTradeItem = 0x059A,
    PakcsShopOpen = 0x059B,
    PakwcShopOpen = 0x059C,
    PakcsShopClose = 0x059D,
    PakwcShopClose = 0x059E,
    PakcsShopListReq = 0x059F,
    PakwcShopListReply = 0x05A0,
    PakcsShopBuyReq = 0x05A1,
    PakcsShopSellReq = 0x05A2,
    PakcsShopBuysellReply = 0x05A3,
    PakcsEquipItemRide = 0x05A4,
    PakwcEquipItemRide = 0x05A5,
    PakcsRepairUseItem = 0x05A6,
    PakcsRepairNpc = 0x05A7,
    PakwcSetItemLife = 0x05A8,
    PakcsPartyReq = 0x05A9,
    PakwcPartyReq = 0x05AA,
    PakcsPartyReply = 0x05AB,
    PakwcPartyReply = 0x05AC,
    PakwcPartyMember = 0x05AD,
    PakwcPartyItem = 0x05AE,
    PakwcPartyLevelexp = 0x05AF,
    PakwcPartyMemberUpdate = 0x05B0,
    PakwcEventAdd = 0x05B1,
    PakcsPartyRule = 0x05B2,
    PakwcPartyRule = 0x05B3,
    PakcsCraftStatus = 0x05B4,
    PakwcCraftStatus = 0x05B5,
    PakcsBankMoveMoney = 0x05B6,
    PakwcBankMoveMoney = 0x05B7,
    PakwcNpcShow = 0x05B8,
    PakwcFairy = 0x05B9,
    PakcsRideRequest = 0x05BA,
    PakwcRideRequest = 0x05BB,
    PakwcBillingMessage = 0x05BC,
    PakwcBillingMessageExt = 0x05BD,
    PakcsClanCommand = 0x05BE,
    PakccClanCommand = 0x05BF,
    PakcsMessenger = 0x05C0,
    PakccMessenger = 0x05C1,
    PakcsMessengerChat = 0x05C2,
    PakccMessengerChat = 0x05C3,
    PakcsChatroom = 0x05C4,
    PakccChatroom = 0x05C5,
    PakcsChatroomMessage = 0x05C6,
    PakccChatroomMessage = 0x05C7,
    PakcsMemo = 0x05C8,
    PakccMemo = 0x05C9,
    PakcsClanIconSet = 0x05CA,
    PakcsClanIconReq = 0x05CB,
    PakccClanIconReply = 0x05CC,
    PakcsClanIconTimestamp = 0x05CD,
    PakccClanIconTimestamp = 0x05CE,
    PakwcRideStateChange = 0x05CF,
    PawkcCharStateChange = 0x05D0,
    PakcsScreenShotTimeReq = 0x05D1,
    PakscScreenShotTimeReply = 0x05D2,
    PakwcUpdateName = 0x05D3,
    PakssAcceptReply = 0x05D4,
    Epacketmax = 0x05D5,
    Stress = 0x6F6D,
}

#[derive(Debug, Error)]
pub enum PacketError {
    #[error("Unknown packet type: {0}")]
    UnknownPacket(u16),
}

impl TryFrom<u16> for PacketType {
    type Error = PacketError;

    fn try_from(value: u16) -> Result<Self, Self::Error> {
        match value {
            0x0500 => Ok(PacketType::PakcsAlive),
            0x0501 => Ok(PacketType::PakssError),
            0x0502 => Ok(PacketType::PakssAnnounceText),
            0x0503 => Ok(PacketType::PakswAnnounceChat),
            0x0504 => Ok(PacketType::PakcsAcceptReq),
            0x0505 => Ok(PacketType::PakcsChannelListReq),
            0x0506 => Ok(PacketType::PaklcChannelListReply),
            0x0507 => Ok(PacketType::PakcsLogoutReq),
            0x0508 => Ok(PacketType::PakwcLogoutReply),
            0x0509 => Ok(PacketType::PakcsLoginReq),
            0x050A => Ok(PacketType::PakcsLoginTokenReq),
            0x050B => Ok(PacketType::PaklcLoginReply),
            0x050C => Ok(PacketType::PakgcLoginReply),
            0x050D => Ok(PacketType::PakcsSrvSelectReq),
            0x050E => Ok(PacketType::PaklcSrvSelectReply),
            0x050F => Ok(PacketType::PakcsJoinServerReq),
            0x0510 => Ok(PacketType::PakcsJoinServerTokenReq),
            0x0511 => Ok(PacketType::PakscJoinServerReply),
            0x0512 => Ok(PacketType::PakwcGmCommand),
            0x0513 => Ok(PacketType::PakwcGlobalVars),
            0x0514 => Ok(PacketType::PakwcGlobalFlags),
            0x0515 => Ok(PacketType::PakccSwitchServer),
            0x0516 => Ok(PacketType::PakcsCharListReq),
            0x0517 => Ok(PacketType::PakccCharListReply),
            0x0518 => Ok(PacketType::PakcsCreateCharReq),
            0x0519 => Ok(PacketType::PakccCreateCharReply),
            0x051A => Ok(PacketType::PakcsDeleteCharReq),
            0x051B => Ok(PacketType::PakccDeleteCharReply),
            0x051C => Ok(PacketType::PakcsSelectCharReq),
            0x051D => Ok(PacketType::PakwcSelectCharReply),
            0x051E => Ok(PacketType::PakwcInventoryData),
            0x051F => Ok(PacketType::PakwcSetMoneyAndItem),
            0x0520 => Ok(PacketType::PakwcSetItem),
            0x0521 => Ok(PacketType::PakwcServerData),
            0x0522 => Ok(PacketType::PakwcQuestData),
            0x0523 => Ok(PacketType::PakcsChangeCharReq),
            0x0524 => Ok(PacketType::PakccChanCharReply),
            0x0525 => Ok(PacketType::PakwcSetMoney),
            0x0526 => Ok(PacketType::PakwcQuestRewardMoney),
            0x0527 => Ok(PacketType::PakwcQuestRewardItem),
            0x0528 => Ok(PacketType::PakwcQuestRewardAddValue),
            0x0529 => Ok(PacketType::PakwcQuestRewardSetValue),
            0x052A => Ok(PacketType::PakcsCancelLogout),
            0x052B => Ok(PacketType::PakwcQuestUpdate),
            0x052C => Ok(PacketType::PakwcWishList),
            0x052D => Ok(PacketType::PakcsQuestDataReq),
            0x052E => Ok(PacketType::PakwcQuestDataReply),
            0x052F => Ok(PacketType::PakwcNpcEvent),
            0x0530 => Ok(PacketType::PakwcGmCommandCode),
            0x0531 => Ok(PacketType::PakcsChangeMapReq),
            0x0532 => Ok(PacketType::PakwcChangeMapReply),
            0x0533 => Ok(PacketType::PakwcInitData),
            0x0534 => Ok(PacketType::PakcsReviveReq),
            0x0535 => Ok(PacketType::PakwcReviveReply),
            0x0536 => Ok(PacketType::PakcsReviveSetPos),
            0x0537 => Ok(PacketType::PakcsSetServerVarReq),
            0x0538 => Ok(PacketType::PakwcSetServerVarReply),
            0x0539 => Ok(PacketType::PakcsCharInfoReq),
            0x053A => Ok(PacketType::PakwcCharInfoReply),
            0x053B => Ok(PacketType::PakcsSetWeightReq),
            0x053C => Ok(PacketType::PakwcSetWeight),
            0x053D => Ok(PacketType::PakwcSetPosition),
            0x053E => Ok(PacketType::PakcsStopMoving),
            0x053F => Ok(PacketType::PakwcStopMoving),
            0x0540 => Ok(PacketType::PakwcUpdateNpc),
            0x0541 => Ok(PacketType::PakcsSummonCmd),
            0x0542 => Ok(PacketType::PakwcSummonCmd),
            0x0543 => Ok(PacketType::PakcsSetAnimation),
            0x0544 => Ok(PacketType::PacwcSetAnimation),
            0x0545 => Ok(PacketType::PakcsToggleMove),
            0x0546 => Ok(PacketType::PakwcToggleMove),
            0x0547 => Ok(PacketType::PakcsNormalChat),
            0x0548 => Ok(PacketType::PakwcNormalChat),
            0x0549 => Ok(PacketType::PakcsWhisperChat),
            0x054A => Ok(PacketType::PakwcWhisperChat),
            0x054B => Ok(PacketType::PakcsShoutChat),
            0x054C => Ok(PacketType::PakwcShoutChat),
            0x054D => Ok(PacketType::PakcsPartyChat),
            0x054E => Ok(PacketType::PakwcPartyChat),
            0x054F => Ok(PacketType::PakcsClanChat),
            0x0550 => Ok(PacketType::PakwcClanChat),
            0x0551 => Ok(PacketType::PakcsAlliedChat),
            0x0552 => Ok(PacketType::PakwcAlliedChat),
            0x0553 => Ok(PacketType::PakcsAlliedShoutChat),
            0x0554 => Ok(PacketType::PakwcAlliedShoutChat),
            0x0555 => Ok(PacketType::PakwcEventStatus),
            0x0556 => Ok(PacketType::PakwcNpcChar),
            0x0557 => Ok(PacketType::PakwcMobChar),
            0x0558 => Ok(PacketType::PakwcPlayerChar),
            0x0559 => Ok(PacketType::PakwcRemoveObject),
            0x055A => Ok(PacketType::PakcsSetPosition),
            0x055B => Ok(PacketType::PakcsStop),
            0x055C => Ok(PacketType::PakwcStop),
            0x055D => Ok(PacketType::PakwcMove),
            0x055E => Ok(PacketType::PakcsAttack),
            0x055F => Ok(PacketType::PakwcAttack),
            0x0560 => Ok(PacketType::PakcsDamage),
            0x0561 => Ok(PacketType::PakwcDamage),
            0x0562 => Ok(PacketType::PakcsMouseCmd),
            0x0563 => Ok(PacketType::PakwcMouseCmd),
            0x0564 => Ok(PacketType::PakwcSetExp),
            0x0565 => Ok(PacketType::PakwcLevelup),
            0x0566 => Ok(PacketType::PakcsHpReq),
            0x0567 => Ok(PacketType::PakwcHpReply),
            0x0568 => Ok(PacketType::PakwcSetHpAndMp),
            0x0569 => Ok(PacketType::PakcsStoreTradeReq),
            0x056A => Ok(PacketType::PakwcStoreTradeReply),
            0x056B => Ok(PacketType::PakcsUseItem),
            0x056C => Ok(PacketType::PakwcUseItem),
            0x056D => Ok(PacketType::PakcsDropItem),
            0x056E => Ok(PacketType::PakcsEquipItem),
            0x056F => Ok(PacketType::PakwcEquipItem),
            0x0570 => Ok(PacketType::PakwcDropItem),
            0x0571 => Ok(PacketType::PakcsPickupItemReq),
            0x0572 => Ok(PacketType::PakwcPickupItemReply),
            0x0573 => Ok(PacketType::PakcsTeleportReq),
            0x0574 => Ok(PacketType::PakwcTeleportReply),
            0x0575 => Ok(PacketType::PakcsStatAddReq),
            0x0576 => Ok(PacketType::PakwcStatAddReply),
            0x0577 => Ok(PacketType::PakcsHotbarSetIconReq),
            0x0578 => Ok(PacketType::PakwcHotbarSetIconReply),
            0x0579 => Ok(PacketType::PakcsEquipProjectile),
            0x057A => Ok(PacketType::PakwcEquipProjectile),
            0x057B => Ok(PacketType::PakwcChangeSkin),
            0x057C => Ok(PacketType::PakcsBankListReq),
            0x057D => Ok(PacketType::PakwcBankListReply),
            0x057E => Ok(PacketType::PakcsBankMoveItem),
            0x057F => Ok(PacketType::PakwcBankMoveItem),
            0x0580 => Ok(PacketType::PakcsCraftReq),
            0x0581 => Ok(PacketType::PakwcCraftReply),
            0x0582 => Ok(PacketType::PakwcSkillLearn),
            0x0583 => Ok(PacketType::PakcsSkillLevelReq),
            0x0584 => Ok(PacketType::PakwcSkillLevelReply),
            0x0585 => Ok(PacketType::PakcsSkillCastSelf),
            0x0586 => Ok(PacketType::PakwcSkillCastSelf),
            0x0587 => Ok(PacketType::PakcsSkillCastTarget),
            0x0588 => Ok(PacketType::PakwcSkillCastTarget),
            0x0589 => Ok(PacketType::PakcsSkillCastPosition),
            0x058A => Ok(PacketType::PakwcSkillCastPosition),
            0x058B => Ok(PacketType::PakwcSkillEffect),
            0x058C => Ok(PacketType::PakwcSkillDamage),
            0x058D => Ok(PacketType::PakwcClearStatus),
            0x058E => Ok(PacketType::PakwcSpeedChanged),
            0x058F => Ok(PacketType::PakwcSkillFinish),
            0x0590 => Ok(PacketType::PakcsAppraisalReq),
            0x0591 => Ok(PacketType::PakwcAppraisalReply),
            0x0592 => Ok(PacketType::PakwcSkillStart),
            0x0593 => Ok(PacketType::PakcsCraftEnhanceReq),
            0x0594 => Ok(PacketType::PakwcCraftEnhanceReply),
            0x0595 => Ok(PacketType::PakwcSkillCancel),
            0x0596 => Ok(PacketType::PakcsWishlistAdd),
            0x0597 => Ok(PacketType::PakcsTrade),
            0x0598 => Ok(PacketType::PakwcTrade),
            0x0599 => Ok(PacketType::PakcsTradeItem),
            0x059A => Ok(PacketType::PakwcTradeItem),
            0x059B => Ok(PacketType::PakcsShopOpen),
            0x059C => Ok(PacketType::PakwcShopOpen),
            0x059D => Ok(PacketType::PakcsShopClose),
            0x059E => Ok(PacketType::PakwcShopClose),
            0x059F => Ok(PacketType::PakcsShopListReq),
            0x05A0 => Ok(PacketType::PakwcShopListReply),
            0x05A1 => Ok(PacketType::PakcsShopBuyReq),
            0x05A2 => Ok(PacketType::PakcsShopSellReq),
            0x05A3 => Ok(PacketType::PakcsShopBuysellReply),
            0x05A4 => Ok(PacketType::PakcsEquipItemRide),
            0x05A5 => Ok(PacketType::PakwcEquipItemRide),
            0x05A6 => Ok(PacketType::PakcsRepairUseItem),
            0x05A7 => Ok(PacketType::PakcsRepairNpc),
            0x05A8 => Ok(PacketType::PakwcSetItemLife),
            0x05A9 => Ok(PacketType::PakcsPartyReq),
            0x05AA => Ok(PacketType::PakwcPartyReq),
            0x05AB => Ok(PacketType::PakcsPartyReply),
            0x05AC => Ok(PacketType::PakwcPartyReply),
            0x05AD => Ok(PacketType::PakwcPartyMember),
            0x05AE => Ok(PacketType::PakwcPartyItem),
            0x05AF => Ok(PacketType::PakwcPartyLevelexp),
            0x05B0 => Ok(PacketType::PakwcPartyMemberUpdate),
            0x05B1 => Ok(PacketType::PakwcEventAdd),
            0x05B2 => Ok(PacketType::PakcsPartyRule),
            0x05B3 => Ok(PacketType::PakwcPartyRule),
            0x05B4 => Ok(PacketType::PakcsCraftStatus),
            0x05B5 => Ok(PacketType::PakwcCraftStatus),
            0x05B6 => Ok(PacketType::PakcsBankMoveMoney),
            0x05B7 => Ok(PacketType::PakwcBankMoveMoney),
            0x05B8 => Ok(PacketType::PakwcNpcShow),
            0x05B9 => Ok(PacketType::PakwcFairy),
            0x05BA => Ok(PacketType::PakcsRideRequest),
            0x05BB => Ok(PacketType::PakwcRideRequest),
            0x05BC => Ok(PacketType::PakwcBillingMessage),
            0x05BD => Ok(PacketType::PakwcBillingMessageExt),
            0x05BE => Ok(PacketType::PakcsClanCommand),
            0x05BF => Ok(PacketType::PakccClanCommand),
            0x05C0 => Ok(PacketType::PakcsMessenger),
            0x05C1 => Ok(PacketType::PakccMessenger),
            0x05C2 => Ok(PacketType::PakcsMessengerChat),
            0x05C3 => Ok(PacketType::PakccMessengerChat),
            0x05C4 => Ok(PacketType::PakcsChatroom),
            0x05C5 => Ok(PacketType::PakccChatroom),
            0x05C6 => Ok(PacketType::PakcsChatroomMessage),
            0x05C7 => Ok(PacketType::PakccChatroomMessage),
            0x05C8 => Ok(PacketType::PakcsMemo),
            0x05C9 => Ok(PacketType::PakccMemo),
            0x05CA => Ok(PacketType::PakcsClanIconSet),
            0x05CB => Ok(PacketType::PakcsClanIconReq),
            0x05CC => Ok(PacketType::PakccClanIconReply),
            0x05CD => Ok(PacketType::PakcsClanIconTimestamp),
            0x05CE => Ok(PacketType::PakccClanIconTimestamp),
            0x05CF => Ok(PacketType::PakwcRideStateChange),
            0x05D0 => Ok(PacketType::PawkcCharStateChange),
            0x05D1 => Ok(PacketType::PakcsScreenShotTimeReq),
            0x05D2 => Ok(PacketType::PakscScreenShotTimeReply),
            0x05D3 => Ok(PacketType::PakwcUpdateName),
            0x05D4 => Ok(PacketType::PakssAcceptReply),
            0x05D5 => Ok(PacketType::Epacketmax),
            0x6F6D => Ok(PacketType::Stress),
            _ => Err(PacketError::UnknownPacket(value)),
        }
    }
}

impl From<PacketType> for u16 {
    fn from(packet_type: PacketType) -> Self {
        packet_type as u16
    }
}
