use crate::enums::{BulletType, EquippedPosition, RidingItem};
pub(crate) const MIN_SELL_TYPE: u32 = 1;
pub(crate) const MAX_SELL_TYPE: u32 = 11;

pub(crate) const MAX_STAT: u32 = 300;
pub(crate) const MAX_STACK: u32 = 999;

pub(crate) const MAX_UNION_COUNT: u32 = 10;
pub(crate) const MAX_BUFF_STATUS: u32 = 40;
pub(crate) const MAX_SKILL_COUNT: u32 = 120;
pub(crate) const MAX_HOTBAR_ITEMS: u32 = 32;

pub(crate) const MAX_DAMAGE: u32 = 99_999_999;
pub(crate) const WALK_SPEED: u32 = 200;
pub(crate) const BASE_MOVE_SPEED: u32 = 425;

pub(crate) const DAMAGE_ACTION_IMMEDIATE: u32 = 0x02;
pub(crate) const DAMAGE_ACTION_HIT: u32 = 0x04;
pub(crate) const DAMAGE_ACTION_CRITICAL: u32 = 0x08;
pub(crate) const DAMAGE_ACTION_DEAD: u32 = 0x10;

pub(crate) const MAX_CONDITIONS_EPISODE: u32 = 5;
pub(crate) const MAX_CONDITIONS_JOB: u32 = 3;
pub(crate) const MAX_CONDITIONS_PLANET: u32 = 7;
pub(crate) const MAX_CONDITIONS_UNION: u32 = 10;
pub(crate) const MAX_QUESTS: u32 = 10;
pub(crate) const MAX_SWITCHES: u32 = 16;

pub(crate) const MAX_QUEST_SWITCHES: u32 = 32;
pub(crate) const MAX_QUEST_VARS: u32 = 10;
pub(crate) const MAX_QUEST_ITEMS: u32 = 6;

pub(crate) const TAB_SIZE: u8 = 30;

pub(crate) const DROP_RANGE: f32 = 50.0;
pub(crate) const MAX_VISIBLE_ITEMS: u32 = 8;
pub(crate) const MAX_INVENTORY: u32 = 120;

// Assuming BulletType, RidingItem, and EquippedPosition enums are already defined as shown previously
pub(crate) const MAX_ITEMS: u32 = MAX_INVENTORY
    + BulletType::MaxBulletTypes as u32
    + RidingItem::MaxRidingItems as u32
    + EquippedPosition::MaxEquipItems as u32;

pub(crate) const FIRST_BULLET_SLOT: u32 = MAX_INVENTORY + EquippedPosition::MaxEquipItems as u32;

pub(crate) const MAX_STATUS_EFFECTS: u32 = 40;

pub(crate) const MAX_WISHLIST: u32 = 30;

// Classes
pub(crate) const CLASS_VISITOR: u32 = 0;
pub(crate) const CLASS_SOLDIER_111: u32 = 111;
pub(crate) const CLASS_SOLDIER_121: u32 = 121;
pub(crate) const CLASS_SOLDIER_122: u32 = 122;
pub(crate) const CLASS_SOLDIER_131: u32 = 131;
pub(crate) const CLASS_SOLDIER_132: u32 = 132;
pub(crate) const CLASS_MAGICIAN_211: u32 = 211;
pub(crate) const CLASS_MAGICIAN_221: u32 = 221;
pub(crate) const CLASS_MAGICIAN_222: u32 = 222;
pub(crate) const CLASS_MAGICIAN_231: u32 = 231;
pub(crate) const CLASS_MAGICIAN_232: u32 = 232;
pub(crate) const CLASS_MIXER_311: u32 = 311;
pub(crate) const CLASS_MIXER_321: u32 = 321;
pub(crate) const CLASS_MIXER_322: u32 = 322;
pub(crate) const CLASS_MIXER_331: u32 = 331;
pub(crate) const CLASS_MIXER_332: u32 = 332;
pub(crate) const CLASS_MERCHANT_411: u32 = 411;
pub(crate) const CLASS_MERCHANT_421: u32 = 421;
pub(crate) const CLASS_MERCHANT_422: u32 = 422;
pub(crate) const CLASS_MERCHANT_431: u32 = 431;
pub(crate) const CLASS_MERCHANT_432: u32 = 432;
