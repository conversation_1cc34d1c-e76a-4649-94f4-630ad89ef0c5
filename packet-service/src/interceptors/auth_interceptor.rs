use tonic::{Request, Status, service::Interceptor};

#[derive(<PERSON><PERSON>, Debug)]
pub struct AuthInterceptor {
    pub client_id: String,
    pub session_id: String,
}

impl Interceptor for AuthInterceptor {
    fn call(&mut self, mut request: Request<()>) -> Result<Request<()>, Status> {
        // Attach the authenticated client ID into the metadata.
        request
            .metadata_mut()
            .insert("x-client-id", self.client_id.parse().unwrap());
        request
            .metadata_mut()
            .insert("x-session-id", self.session_id.parse().unwrap());
        Ok(request)
    }
}