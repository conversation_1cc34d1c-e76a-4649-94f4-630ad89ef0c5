use bincode::{Decode, Encode};

// `HotbarItem` structure converted to Rust.
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Encode, Decode, Default)]
pub(crate) struct HotbarItem {
    type_: u8,
    slot_id: u16,
}

// `Skill` structure converted to Rust.
#[derive(Debug, <PERSON>lone, Co<PERSON>, PartialEq, Eq, Encode, Decode, Default)]
pub(crate) struct Skill {
    id: u16,
    level: u8,
}

// `StatusEffect` structure converted to Rust.
#[derive(Debug, <PERSON>lone, Copy, PartialEq, Eq, Encode, Decode, Default)]
pub(crate) struct StatusEffect {
    expired: u32,
    value: u16,
    unknown: u16,
}
