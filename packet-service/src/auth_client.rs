use crate::auth::auth_service_client::AuthServiceClient;
use crate::auth::{
    LoginRequest, LoginResponse, LogoutRequest, RefreshSessionResponse, ValidateSessionRequest,
    ValidateSessionResponse, ValidateTokenRequest, ValidateTokenResponse,
};
use crate::common::Empty;
use tonic::transport::Channel;

#[derive(<PERSON><PERSON>, Debug)]
pub struct AuthClient {
    client: AuthServiceClient<Channel>,
}

impl AuthClient {
    pub async fn connect(endpoint: &str) -> Result<Self, Box<dyn std::error::Error>> {
        let client = AuthServiceClient::connect(endpoint.to_string()).await?;
        Ok(AuthClient { client })
    }

    pub async fn login(
        &mut self,
        username: &str,
        password: &str,
        ip_address: &str,
    ) -> Result<LoginResponse, Box<dyn std::error::Error + Send + Sync>> {
        let request = LoginRequest {
            username: username.to_string(),
            password: password.to_string(),
            ip_address: ip_address.to_string(),
        };

        let response = self.client.login(request).await?;
        Ok(response.into_inner())
    }

    pub async fn login_token(
        &mut self,
        token: &str,
    ) -> Result<ValidateTokenResponse, Box<dyn std::error::Error + Send + Sync>> {
        let request = ValidateTokenRequest {
            token: token.to_string(),
        };

        let response = self.client.validate_token(request).await?;
        Ok(response.into_inner())
    }

    pub async fn validate_session(
        &mut self,
        session_id: &str,
    ) -> Result<ValidateSessionResponse, Box<dyn std::error::Error + Send + Sync>> {
        let request = ValidateSessionRequest {
            session_id: session_id.to_string(),
        };

        let response = self.client.validate_session(request).await?;
        Ok(response.into_inner())
    }

    pub async fn refresh_session(
        &mut self,
        session_id: &str,
    ) -> Result<RefreshSessionResponse, Box<dyn std::error::Error + Send + Sync>> {
        let request = ValidateSessionRequest {
            session_id: session_id.to_string(),
        };

        let response = self.client.refresh_session(request).await?;
        Ok(response.into_inner())
    }

    pub async fn logout(&mut self, session_id: &str) -> Result<Empty, Box<dyn std::error::Error + Send + Sync>> {
        let request = LogoutRequest {
            session_id: session_id.to_string(),
        };

        let response = self.client.logout(request).await?;
        Ok(response.into_inner())
    }
}
