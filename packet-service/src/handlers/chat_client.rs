use tonic::{Request, transport::Channel};
use futures::StreamExt;
use tokio::sync::{mpsc, Mutex};
use tokio_stream::wrappers::ReceiverStream;
use std::error::Error;
use tracing::{debug, error};

pub mod chat {
    tonic::include_proto!("chat");
}

use chat::chat_service_client::ChatServiceClient;
use chat::ChatMessage;
use crate::interceptors::auth_interceptor::AuthInterceptor;

/// ChatClientHandler encapsulates the bidirectional chat stream.
/// In addition to providing an API to send messages, it also spawns a
/// background task which forwards incoming chat messages through an inbound channel.
pub struct ChatClientHandler {
    outbound_tx: mpsc::Sender<ChatMessage>,
    /// Inbound messages from the chat service are sent here.
    pub inbound_rx: Mutex<mpsc::Receiver<ChatMessage>>,
}

impl ChatClientHandler {
    /// Creates and returns a new ChatClientHandler.
    ///
    /// * `chat_url` - Full URL of the Chat Service (for example, "http://127.0.0.1:50051")
    /// * `client_id` - The authenticated client ID to be injected into each request.
    /// * `session_id` - The authenticated session token to be injected into each request.
    pub async fn new(
        chat_url: String,
        client_id: String,
        session_id: String,
    ) -> Result<Self, Box<dyn Error + Send + Sync>> {
        // Create a channel to the Chat Service.
        let channel = Channel::from_shared(chat_url)?.connect().await
            .map_err(|e| Box::new(e) as Box<dyn Error + Send + Sync>)?;
        let interceptor = AuthInterceptor { client_id, session_id };

        // Create ChatService client with interceptor.
        let mut chat_client = ChatServiceClient::with_interceptor(channel, interceptor);

        // Create an mpsc channel for outbound messages.
        let (out_tx, out_rx) = mpsc::channel(32);
        let outbound_stream = ReceiverStream::new(out_rx);

        // This channel will be used to forward inbound messages to the packet-service.
        let (in_tx, in_rx) = mpsc::channel(32);

        // Establish the bidirectional chat stream.
        let request = Request::new(outbound_stream);
        let mut response = chat_client.chat_stream(request).await
            .map_err(|e| Box::new(e) as Box<dyn Error + Send + Sync>)?.into_inner();

        // Spawn a task to continuously receive messages from the Chat Service.
        // Each received message is sent through the 'in_tx' channel.
        tokio::spawn(async move {
            while let Some(result) = response.next().await {
                match result {
                    Ok(chat_msg) => {
                        // You might translate or process the chat_msg here,
                        // then forward it to your packet-service logic.
                        if let Err(e) = in_tx.send(chat_msg).await {
                            error!("Failed to forward chat message: {:?}", e);
                            break;
                        }
                    }
                    Err(e) => {
                        error!("Error receiving chat stream message: {:?}", e);
                        break;
                    }
                }
            }
            debug!("Chat inbound stream closed");
        });

        Ok(Self {
            outbound_tx: out_tx,
            inbound_rx: Mutex::new(in_rx),
        })
    }

    /// Sends a chat message to the Chat Service.
    pub async fn send_message(
        &self,
        message: ChatMessage,
    ) -> Result<(), Box<dyn Error + Send + Sync>> {
        self.outbound_tx.send(message).await?;
        Ok(())
    }
}