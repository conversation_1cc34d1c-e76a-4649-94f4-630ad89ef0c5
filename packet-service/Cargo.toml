[package]
name = "packet-service"
version = "0.1.0"
edition = "2021"

# Define both a binary and a library target
[[bin]]
name = "packet-service"
path = "src/main.rs"

[lib]
name = "packet_service"
path = "src/lib.rs"

[features]
mocks = []
consul = []

[dependencies]
dotenv = "0.15"
tokio = { version = "1.41.1", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
bytes = { version = "1.8.0", features = ["std", "serde"] }
tracing = "0.1"
tracing-subscriber = { version = "0.3.19", features = ["env-filter", "chrono"] }
bincode = { version = "2.0.0", features = ["derive", "serde"] }
thiserror = "2.0.3"
lazy_static = "1.5.0"
prometheus = "0.13.4"
hyper = { version = "1.5.1", features = ["server"] }
tonic = "0.12.3"
prost = "0.13.4"
utils = { path = "../utils" }
warp = "0.3.7"
dashmap = "6.1.0"
uuid = { version = "1.11.0", features = ["v4"] }
chrono = "0.4.39"
prometheus_exporter = "0.8.5"
futures = "0.3.31"
tokio-stream = "0.1.17"

[build-dependencies]
tonic-build = "0.12.3"
