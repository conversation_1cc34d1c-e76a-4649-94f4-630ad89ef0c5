# Packet Service

The Packet Service handles communication between game clients and the MMORPG server using a custom binary packet protocol.

## Overview

The Packet Service is responsible for:
- Accepting TCP connections from game clients
- Parsing binary packet data
- Routing packets to appropriate handlers
- Sending response packets back to clients
- Managing client connection state

## Architecture

The service is built using the following components:

- **TCP Server**: Accepts client connections
- **Buffer Pool**: Efficiently manages memory for packet processing
- **Packet Router**: Routes packets to appropriate handlers
- **Connection Service**: Manages client connection state
- **Packet Handlers**: Process specific packet types
- **Auth/Character Clients**: Communicate with other services

## Packet Structure

Each packet follows a standard binary format:

```
+----------------+----------------+----------------+----------------+
| Packet Size    | Packet Type    | Packet CRC     | Payload        |
| (2 bytes)      | (2 bytes)      | (2 bytes)      | (variable)     |
+----------------+----------------+----------------+----------------+
```

- **Packet Size**: Total size of the packet in bytes (including header)
- **Packet Type**: Identifies the packet type (see `packet_type.rs`)
- **Packet CRC**: Checksum for packet validation
- **Payload**: Packet-specific data

## Packet Types

The service supports numerous packet types for different game operations:

- **Authentication**: Login, logout, server selection
- **Character**: Character creation, deletion, selection
- **Chat**: Normal chat, whispers, shouts, party chat
- **Movement**: Position updates, teleportation
- **Combat**: Attacks, skills, damage
- **Items**: Inventory management, equipment
- **Party**: Party formation, invitations
- **Trade**: Item trading between players
- **Shop**: NPC shop interactions

See `packet_type.rs` for a complete list of supported packet types.

## Connection State

The service maintains state for each client connection, including:

- User ID
- Session ID
- Character ID
- Character list
- Additional session data

## Metrics

The service exposes Prometheus metrics for monitoring:

- Active connections
- Packets received/sent
- Packet processing time

## Configuration

The service can be configured using environment variables:

- `LISTEN_ADDR`: The address to listen on (default: "0.0.0.0")
- `SERVICE_PORT`: The port to listen on (default: "29000")
- `PACKET_METRICS_PORT`: Port for Prometheus metrics (default: "9102")
- `MAX_CONCURRENT_CONNECTIONS`: Maximum allowed concurrent connections
- `BUFFER_POOL_SIZE`: Size of the packet buffer pool
- `LOG_LEVEL`: Logging level (default: "info")

## Running the Service

### Local Development

```bash
cargo run
```

### Docker

```bash
docker build -t packet-service .
docker run -p 29000:29000 -p 9102:9102 packet-service
```

## Integration with External Systems

The Packet Service integrates with:

- **Auth Service**: For user authentication and session validation
- **Character Service**: For character management
- **World Service**: For game world interactions
