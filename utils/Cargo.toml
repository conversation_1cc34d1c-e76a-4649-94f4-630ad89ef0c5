[package]
name = "utils"
version = "0.1.0"
edition = "2021"

[dependencies]
serde = { version = "1.0", features = ["derive"] }
reqwest = { version = "0.12.12", features = ["json"] }
tracing = "0.1"
uuid = { version = "1.15.1", features = ["v4"] }
warp = "0.3.7"
tokio = { version = "1.43.0", features = ["full"] }
bincode = { version = "2.0.0", features = ["derive", "serde"] }
redis = "0.29.1"
deadpool-redis = "0.20.0"
async-trait = "0.1.87"
serde_json = "1.0.140"
hickory-resolver = "0.24.4"
rand = "0.8.5"
kube = { version = "0.99.0", features = ["derive"] }
k8s-openapi = { version = "0.24.0", features = ["v1_32"] }
tracing-subscriber = { version = "0.3.19", features = ["env-filter", "chrono"] }
