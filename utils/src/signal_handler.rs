use tokio::{select, signal};
use tracing::info;

pub async fn wait_for_signal() {
    select! {
        _ = signal::ctrl_c() => {
            info!("Received SIGINT (Ctrl+C), shutting down...");
        },
        _ = terminate_signal() => {
            info!("Received termination signal, shutting down...");
        },
    }
}

async fn terminate_signal() {
    #[cfg(unix)]
    {
        use tokio::signal::unix::{signal, SignalKind};
        let mut sigterm = signal(SignalKind::terminate()).expect("Failed to set up SIGTERM handler");
        sigterm.recv().await;
    }

    #[cfg(windows)]
    {
        let mut ctrlbreak = signal::windows::ctrl_break().expect("Failed to set up CTRL_BREAK handler");
        ctrlbreak.recv().await;
    }
}
