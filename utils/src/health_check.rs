use std::env;
use std::net::ToSocketAddrs;
use warp::Filter;

pub async fn start_health_check(service_address: &str) -> Result<(), Box<dyn std::error::Error>> {
    let health_port = env::var("HEALTH_CHECK_PORT").unwrap_or_else(|_| "8082".to_string());
    let health_check_endpoint_addr = format!("{}:{}", service_address, health_port);

    // Start health-check endpoint
    let log = warp::log("health_check");
    let health_route = warp::path!("health")
        .map(|| warp::reply::with_status("OK", warp::http::StatusCode::OK))
        .with(log);

    tokio::spawn(warp::serve(health_route).run(health_check_endpoint_addr.to_socket_addrs()?.next().unwrap()));

    Ok(())
}
