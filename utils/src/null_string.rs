use bincode::de::read::Reader;
use bincode::enc::write::Writer;
use bincode::{
    de::Decoder,
    enc::Encoder,
    error::{DecodeError, EncodeError},
    Decode, Encode,
};
use std::str;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, De<PERSON>ult)]
pub struct NullTerminatedString(pub String);

impl NullTerminatedString {
    pub fn new(string: &str) -> Self {
        NullTerminatedString(string.into())
    }
}

impl Encode for NullTerminatedString {
    fn encode<E: Encoder>(&self, encoder: &mut E) -> Result<(), EncodeError> {
        let bytes = self.0.as_bytes();
        encoder.writer().write(bytes)?; // Write the string bytes
        encoder.writer().write(&[0])?; // Add the null terminator
        Ok(())
    }
}

impl<Context> Decode<Context> for NullTerminatedString {
    fn decode<D: Decoder>(decoder: &mut D) -> Result<Self, DecodeError> {
        let mut buffer = Vec::new();
        let mut byte = [0u8; 1];

        // Read until the null terminator
        while decoder.reader().read(&mut byte).is_ok() {
            if byte[0] == 0 {
                break; // Null terminator found
            }
            buffer.push(byte[0]);
        }

        let string = str::from_utf8(&buffer)
            .map_err(|e| DecodeError::OtherString(format!("Invalid UTF-8: {}", e)))?
            .to_string();

        Ok(NullTerminatedString(string))
    }
}
