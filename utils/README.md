# Utils Module

This module provides shared utilities used by all microservices in the MMORPG server architecture.

## Components

### Service Discovery

The `service_discovery.rs` module provides functionality for discovering services in both Kubernetes and Consul environments:

- `get_service_endpoints_by_dns`: Discovers service endpoints using Consul DNS
- `get_kube_service_endpoints_by_dns`: Discovers service endpoints using Kubernetes DNS
- `get_service_info`: Retrieves detailed information about a Kubernetes service
- `get_services_by_label`: Finds Kubernetes services matching specific labels

### Redis Cache

The `redis_cache.rs` module provides a caching layer using Redis:

- Implements the `Cache` trait for standardized cache operations
- Provides methods for getting, setting, and deleting cached values
- Supports TTL (Time To Live) for cached entries

### Multi-Service Load Balancer

The `multi_service_load_balancer.rs` module provides load balancing across multiple service instances:

- Supports Random and Round-Robin load balancing strategies
- Dynamically refreshes service endpoints
- Provides failover capabilities

### Signal Handler

The `signal_handler.rs` module provides graceful shutdown capabilities:

- `wait_for_signal`: Waits for termination signals (SIGINT, SIGTERM)
- Cross-platform support for Unix and Windows signals

### Consul Registration

The `consul_registration.rs` module provides service registration with Consul:

- `register_service`: Registers a service with Consul
- `generate_service_id`: Generates unique service IDs
- `get_or_generate_service_id`: Retrieves or creates service IDs

### Health Check

The `health_check.rs` module provides HTTP health check endpoints:

- `start_health_check`: Starts a health check endpoint on a specified port

### Logging

The `logging.rs` module provides standardized logging setup:

- `setup_logging`: Configures tracing with appropriate log levels

## Usage

Import the required utilities in your service:

```rust
use utils::logging;
use utils::service_discovery::get_kube_service_endpoints_by_dns;
use utils::signal_handler::wait_for_signal;

// Setup logging
logging::setup_logging("my-service", &["my_service"]);

// Discover services
let db_url = format!("http://{}", 
    get_kube_service_endpoints_by_dns("database-service", "tcp", "database-service")
    .await?
    .get(0)
    .unwrap()
);

// Wait for termination signal
wait_for_signal().await;
```
