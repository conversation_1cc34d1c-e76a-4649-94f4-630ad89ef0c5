FROM rust:alpine AS builder
LABEL authors="raven"

RUN apk add --no-cache musl-dev libressl-dev protobuf-dev

WORKDIR /usr/src/utils
COPY ./utils .

WORKDIR /usr/src/proto
COPY ./proto .

WORKDIR /usr/src/world-service
COPY ./world-service .

RUN cargo build --release

FROM alpine:3

RUN apk add --no-cache libssl3 libgcc

COPY --from=builder /usr/src/world-service/target/release/world-service /usr/local/bin/world-service

EXPOSE 50054

CMD ["world-service"]