use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::{mpsc, Mutex};
use tokio::time::{sleep, Duration};
use tonic::transport::Channel;
use tracing::{debug, error, info, warn};
use futures::StreamExt;

pub mod world {
    tonic::include_proto!("world");
}

pub mod game_logic {
    tonic::include_proto!("game_logic");
}

use world::world_game_logic_service_client::WorldGameLogicServiceClient;
use game_logic::game_logic_service_client::GameLogicServiceClient;

pub struct GameLogicClientManager {
    clients: Arc<Mutex<HashMap<u32, GameLogicClient>>>,
}

pub struct GameLogicClient {
    pub map_id: u32,
    pub endpoint: String,
    pub service_client: Option<GameLogicServiceClient<Channel>>,
    pub world_client: Option<WorldGameLogicServiceClient<Channel>>,
    pub event_sender: Option<mpsc::UnboundedSender<world::GameLogicEvent>>,
}

impl GameLogicClientManager {
    pub fn new() -> Self {
        Self {
            clients: Arc::new(Mutex::new(HashMap::new())),
        }
    }

    /// Get retry configuration from environment variables with sensible defaults
    fn get_retry_config() -> (u32, Duration, Duration) {
        let max_retries = std::env::var("GAME_LOGIC_MAX_RETRIES")
            .unwrap_or_else(|_| "5".to_string())
            .parse::<u32>()
            .unwrap_or(5);

        let initial_delay_ms = std::env::var("GAME_LOGIC_INITIAL_DELAY_MS")
            .unwrap_or_else(|_| "500".to_string())
            .parse::<u64>()
            .unwrap_or(500);

        let max_delay_ms = std::env::var("GAME_LOGIC_MAX_DELAY_MS")
            .unwrap_or_else(|_| "10000".to_string())
            .parse::<u64>()
            .unwrap_or(10000);

        (
            max_retries,
            Duration::from_millis(initial_delay_ms),
            Duration::from_millis(max_delay_ms),
        )
    }

    pub async fn add_client(&self, map_id: u32, endpoint: String) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let (max_retries, initial_delay, max_delay) = Self::get_retry_config();
        self.add_client_with_retry(map_id, endpoint, max_retries, initial_delay, max_delay).await
    }

    pub async fn add_client_with_retry(
        &self,
        map_id: u32,
        endpoint: String,
        max_retries: u32,
        initial_delay: Duration,
        max_delay: Duration,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let mut clients = self.clients.lock().await;

        if clients.contains_key(&map_id) {
            warn!("Game logic client for map {} already exists", map_id);
            return Ok(());
        }

        // Release the lock before attempting connections
        drop(clients);

        let mut client = GameLogicClient {
            map_id,
            endpoint: endpoint.clone(),
            service_client: None,
            world_client: None,
            event_sender: None,
        };

        // Retry logic for connecting to the game logic service
        let mut delay = initial_delay;
        let mut last_error = None;

        for attempt in 0..=max_retries {
            match GameLogicServiceClient::connect(endpoint.clone()).await {
                Ok(service_client) => {
                    client.service_client = Some(service_client);
                    info!("Connected to game logic service for map {} at {} (attempt {})", map_id, endpoint, attempt + 1);
                    break;
                }
                Err(e) => {
                    last_error = Some(e);
                    if attempt < max_retries {
                        warn!("Failed to connect to game logic service for map {} at {} (attempt {}): {}. Retrying in {:?}...",
                              map_id, endpoint, attempt + 1, last_error.as_ref().unwrap(), delay);
                        sleep(delay).await;
                        delay = std::cmp::min(delay * 2, max_delay);
                    } else {
                        error!("Failed to connect to game logic service for map {} at {} after {} attempts: {}",
                               map_id, endpoint, max_retries + 1, last_error.as_ref().unwrap());
                        return Err(last_error.unwrap().into());
                    }
                }
            }
        }

        // Reset delay for the second connection
        delay = initial_delay;
        last_error = None;

        // Retry logic for connecting to the world-game-logic service
        for attempt in 0..=max_retries {
            match WorldGameLogicServiceClient::connect(endpoint.clone()).await {
                Ok(world_client) => {
                    client.world_client = Some(world_client);
                    info!("Connected to game logic world service for map {} at {} (attempt {})", map_id, endpoint, attempt + 1);
                    break;
                }
                Err(e) => {
                    last_error = Some(e);
                    if attempt < max_retries {
                        warn!("Failed to connect to game logic world service for map {} at {} (attempt {}): {}. Retrying in {:?}...",
                              map_id, endpoint, attempt + 1, last_error.as_ref().unwrap(), delay);
                        sleep(delay).await;
                        delay = std::cmp::min(delay * 2, max_delay);
                    } else {
                        error!("Failed to connect to game logic world service for map {} at {} after {} attempts: {}",
                               map_id, endpoint, max_retries + 1, last_error.as_ref().unwrap());
                        return Err(last_error.unwrap().into());
                    }
                }
            }
        }

        // Re-acquire the lock and insert the client
        let mut clients = self.clients.lock().await;
        clients.insert(map_id, client);
        info!("Successfully added game logic client for map {} at {}", map_id, endpoint);
        Ok(())
    }

    pub async fn remove_client(&self, map_id: u32) {
        let mut clients = self.clients.lock().await;
        if let Some(_client) = clients.remove(&map_id) {
            info!("Removed game logic client for map {}", map_id);
        }
    }

    pub async fn get_nearby_objects(
        &self,
        map_id: u32,
        session_id: String,
        x: f32,
        y: f32,
        z: f32,
    ) -> Result<game_logic::NearbyObjectsResponse, Box<dyn std::error::Error + Send + Sync>> {
        let mut clients = self.clients.lock().await;
        
        if let Some(client) = clients.get_mut(&map_id) {
            if let Some(service_client) = &mut client.service_client {
                let request = game_logic::NearbyObjectsRequest {
                    session_id,
                    x,
                    y,
                    z,
                    map_id: map_id as i32,
                };

                let response = service_client.get_nearby_objects(request).await?;
                return Ok(response.into_inner());
            }
        }

        Err(format!("No game logic client found for map {}", map_id).into())
    }

    pub async fn start_event_stream(
        &self,
        map_id: u32,
        outbound_receiver: mpsc::UnboundedReceiver<world::GameLogicEvent>,
    ) -> Result<mpsc::UnboundedReceiver<world::GameLogicEvent>, Box<dyn std::error::Error + Send + Sync>> {
        let mut clients = self.clients.lock().await;

        if let Some(client) = clients.get_mut(&map_id) {
            if let Some(mut world_client) = client.world_client.take() {
                let (inbound_sender, inbound_receiver) = mpsc::unbounded_channel();

                // Create the bidirectional stream
                let outbound_stream = tokio_stream::wrappers::UnboundedReceiverStream::new(outbound_receiver);

                let response = world_client.stream_game_events(outbound_stream).await?;
                let mut inbound_stream = response.into_inner();

                // Spawn task to handle incoming events
                tokio::spawn(async move {
                    while let Some(event) = inbound_stream.next().await {
                        match event {
                            Ok(game_event) => {
                                debug!("Received event from game logic for map {}: {:?}", map_id, game_event);
                                if let Err(e) = inbound_sender.send(game_event) {
                                    error!("Failed to forward event from game logic for map {}: {}", map_id, e);
                                    break;
                                }
                            }
                            Err(e) => {
                                error!("Error receiving event from game logic for map {}: {}", map_id, e);
                                break;
                            }
                        }
                    }
                    info!("Event stream from game logic for map {} ended", map_id);
                });

                return Ok(inbound_receiver);
            }
        }

        Err(format!("No world client found for map {}", map_id).into())
    }

    pub async fn send_event(&self, map_id: u32, event: world::GameLogicEvent) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let clients = self.clients.lock().await;

        if let Some(client) = clients.get(&map_id) {
            if let Some(sender) = &client.event_sender {
                sender.send(event)?;
                return Ok(());
            }
        }

        Err(format!("No event sender found for map {}", map_id).into())
    }

    pub async fn list_connected_maps(&self) -> Vec<u32> {
        let clients = self.clients.lock().await;
        clients.keys().cloned().collect()
    }
}

impl GameLogicClient {
    pub async fn connect(map_id: u32, endpoint: String) -> Result<Self, Box<dyn std::error::Error + Send + Sync>> {
        let service_client = GameLogicServiceClient::connect(endpoint.clone()).await?;
        let world_client = WorldGameLogicServiceClient::connect(endpoint.clone()).await?;

        Ok(Self {
            map_id,
            endpoint,
            service_client: Some(service_client),
            world_client: Some(world_client),
            event_sender: None,
        })
    }
}
