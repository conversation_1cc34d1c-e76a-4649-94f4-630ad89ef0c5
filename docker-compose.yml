services:
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    restart: on-failure
    container_name: frontend
    networks:
      - frontend
    ports:
      - "3000:80"
    env_file:
      - .env
    depends_on:
      - api-service
      - consul

  auth-service:
    build:
      context: ./
      dockerfile: ./auth-service/Dockerfile
    restart: on-failure
    networks:
      - backend
    ports:
      - "50051:50051"
    env_file:
      - ./auth-service/.env
      - .env
    environment:
      - HEALTH_CHECK_PORT=8080
    depends_on:
      - session-service
      - database-service
      - consul
    volumes:
      - service_ids:/services

  api-service:
    build:
      context: ./
      dockerfile: ./api-service/Dockerfile
    restart: on-failure
    networks:
      - frontend
      - backend
    ports:
      - "8080:8080"
      - "8081:8081"
    env_file:
      - ./api-service/.env
      - .env
    depends_on:
      - auth-service
      - consul
    volumes:
      - service_ids:/services

  database-service:
    build:
      context: ./
      dockerfile: ./database-service/Dockerfile
    restart: on-failure
    networks:
      - backend
    ports:
      - "50052:50052"
    env_file:
      - ./database-service/.env
      - .env
    depends_on:
      - db
      - consul
    volumes:
      - service_ids:/services

  character-service:
    build:
      context: ./
      dockerfile: ./character-service/Dockerfile
    restart: on-failure
    networks:
      - backend
    ports:
      - "50053:50053"
    env_file:
      - ./character-service/.env
      - .env
    depends_on:
      - auth-service
      - consul
    volumes:
      - service_ids:/services

  world-service:
    build:
      context: ./
      dockerfile: ./world-service/Dockerfile
    restart: on-failure
    networks:
      - backend
    ports:
      - "50054:50054"
    env_file:
      - ./world-service/.env
      - .env
    depends_on:
      - auth-service
      - consul
    volumes:
      - service_ids:/services

  packet-service:
    build:
      context: ./
      dockerfile: ./packet-service/Dockerfile
    restart: on-failure
    networks:
      - backend
    ports:
      - "29000:29000"
      - "4001:4001"
      - "8082:8082"
    env_file:
      - ./packet-service/.env
      - .env
    depends_on:
      - auth-service
      - consul
    volumes:
      - service_ids:/services

  session-service:
    build:
      context: ./
      dockerfile: ./session-service/Dockerfile
    restart: on-failure
    networks:
      - backend
    ports:
      - "50055:50055"
    env_file:
      - ./session-service/.env
      - .env
    depends_on:
      - consul
    volumes:
      - service_ids:/services

  cache:
    image: valkey/valkey:alpine
    env_file:
      - .env
    restart: on-failure
    networks:
      backend:
        ipv4_address: ************
    ports:
      - "6379:6379"
    volumes:
      - cache_data:/data

  db:
    image: postgres:17
    env_file:
      - .env
    restart: on-failure
    networks:
      backend:
        ipv4_address: ************
    ports:
      - "5432:5432"
    volumes:
      - db_data:/var/lib/postgresql/data
  #      - ./sql/schema.sql:/docker-entrypoint-initdb.d/schema.sql:ro

  consul:
    image: hashicorp/consul:latest
    command: [
      "agent",
      "-dev",
      "-bootstrap-expect=1",
      "-client=0.0.0.0",
      "-bind=0.0.0.0",
    ]
    restart: on-failure
    networks:
      backend:
        ipv4_address: ************
    ports:
      - "8500:8500"
      - "8600:8600/udp"
      - "8600:8600/tcp"
    volumes:
      - ./scripts/consul.json:/consul/config/cors.json

volumes:
  db_data:
  cache_data:
  service_ids:

networks:
  backend:
    ipam:
      driver: default
      config:
        - subnet: "************/24"
        - subnet: "2001:3984:3989::/64"
  frontend:
    driver: bridge