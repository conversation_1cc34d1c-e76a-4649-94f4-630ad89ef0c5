use auth_service::users::{hash_password, verify_password};

#[test]
fn test_password_hashing_and_verification() {
    // Test with a simple password
    let password = "test_password";
    let hashed = hash_password(password);
    
    // Verify the hash is not the same as the original password
    assert_ne!(password, hashed);
    
    // Verify the password against the hash
    assert!(verify_password(password, &hashed));
    
    // Verify an incorrect password fails
    assert!(!verify_password("wrong_password", &hashed));
}

#[test]
fn test_password_hashing_with_special_characters() {
    // Test with special characters
    let password = "P@$$w0rd!#%^&*()";
    let hashed = hash_password(password);
    
    // Verify the hash is not the same as the original password
    assert_ne!(password, hashed);
    
    // Verify the password against the hash
    assert!(verify_password(password, &hashed));
}

#[test]
fn test_password_hashing_with_unicode() {
    // Test with Unicode characters
    let password = "пароль123你好世界";
    let hashed = hash_password(password);
    
    // Verify the hash is not the same as the original password
    assert_ne!(password, hashed);
    
    // Verify the password against the hash
    assert!(verify_password(password, &hashed));
}

#[test]
fn test_different_passwords_produce_different_hashes() {
    let password1 = "password1";
    let password2 = "password2";
    
    let hash1 = hash_password(password1);
    let hash2 = hash_password(password2);
    
    // Different passwords should produce different hashes
    assert_ne!(hash1, hash2);
}

#[test]
fn test_same_password_produces_different_hashes() {
    let password = "same_password";
    
    let hash1 = hash_password(password);
    let hash2 = hash_password(password);
    
    // Same password should produce different hashes due to salt
    assert_ne!(hash1, hash2);
    
    // But both hashes should verify against the original password
    assert!(verify_password(password, &hash1));
    assert!(verify_password(password, &hash2));
}
