[package]
name = "mmorpg-server-tests"
version = "0.1.0"
edition = "2021"
publish = false

[lib]
proc-macro = false
test = true
doctest = false
bench = false
doc = false
plugin = false
harness = true

[dependencies]
auth-service = { path = "../auth-service" }
character-service = { path = "../character-service" }
database-service = { path = "../database-service" }
packet-service = { path = "../packet-service" }
utils = { path = "../utils" }

# Common dependencies
tokio = { version = "1.36.0", features = ["full"] }
tonic = "0.11.0"
bincode = { version = "2.0.0", features = ["derive", "serde"] }
serde = { version = "1.0.197", features = ["derive"] }
mockall = "0.12.1"
reqwest = { version = "0.11.24", features = ["json"] }
tracing = "0.1.40"
chrono = "0.4.35"
sqlx = "0.8.3"
serde_json = "1.0.140"
dotenv = "0.15"

[[test]]
name = "auth_users_tests"
path = "auth-service/users_tests.rs"

[[test]]
name = "character_service_tests"
path = "character-service/character_service_tests.rs"

[[test]]
name = "packet_tests"
path = "packet-service/packet_tests.rs"

[[test]]
name = "bufferpool_tests"
path = "packet-service/bufferpool_tests.rs"

[[test]]
name = "connection_service_tests"
path = "packet-service/connection_service_tests.rs"

[[test]]
name = "redis_cache_tests"
path = "utils/redis_cache_tests.rs"

[[test]]
name = "service_discovery_tests"
path = "utils/service_discovery_tests.rs"

[[test]]
name = "multi_service_load_balancer_tests"
path = "utils/multi_service_load_balancer_tests.rs"

[[test]]
name = "health_check_tests"
path = "utils/health_check_tests.rs"

[[test]]
name = "logging_tests"
path = "utils/logging_tests.rs"

[[test]]
name = "get_user_tests"
path = "database-service/get_user.rs"

[[test]]
name = "grpc_get_user_tests"
path = "database-service/grpc_get_user.rs"

[[test]]
name = "integration_tests"
path = "database-service/integration.rs"

[[test]]
name = "mock_tests"
path = "database-service/mock_tests.rs"

[[test]]
name = "redis_cache_db_tests"
path = "database-service/redis_cache.rs"
