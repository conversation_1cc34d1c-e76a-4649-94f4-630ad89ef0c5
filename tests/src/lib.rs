// This file is required to make the tests directory a proper Rust library crate
// It will only be compiled when running tests

#[cfg(test)]
pub mod test_utils {
    // Common test utilities can go here
    pub fn setup() {
        // Common test setup code
    }

    pub fn teardown() {
        // Common test teardown code
    }
}

// This ensures the crate is only compiled during testing
#[cfg(not(test))]
compile_error!("This crate is only meant to be used in test mode");
