use packet_service::connection_service::ConnectionService;
use packet_service::connection_state::ConnectionState;
use std::collections::HashSet;

#[test]
fn test_connection_service_add_connection() {
    let service = ConnectionService::new();
    
    // Add a connection
    let connection_id = service.add_connection();
    
    // Verify the connection exists
    let connection = service.get_connection(&connection_id);
    assert!(connection.is_some());
}

#[test]
fn test_connection_service_remove_connection() {
    let service = ConnectionService::new();
    
    // Add a connection
    let connection_id = service.add_connection();
    
    // Verify the connection exists
    let connection = service.get_connection(&connection_id);
    assert!(connection.is_some());
    
    // Remove the connection
    service.remove_connection(&connection_id);
    
    // Verify the connection no longer exists
    let connection = service.get_connection(&connection_id);
    assert!(connection.is_none());
}

#[test]
fn test_connection_service_get_connection_mut() {
    let service = ConnectionService::new();
    
    // Add a connection
    let connection_id = service.add_connection();
    
    // Get a mutable reference to the connection
    let mut connection = service.get_connection_mut(&connection_id).unwrap();
    
    // Modify the connection
    connection.user_id = Some("test_user".to_string());
    connection.session_id = Some("test_session".to_string());
    connection.character_id = Some(123);
    
    // Drop the mutable reference
    drop(connection);
    
    // Verify the changes were saved
    let connection = service.get_connection(&connection_id).unwrap();
    assert_eq!(connection.user_id, Some("test_user".to_string()));
    assert_eq!(connection.session_id, Some("test_session".to_string()));
    assert_eq!(connection.character_id, Some(123));
}

#[test]
fn test_connection_service_multiple_connections() {
    let service = ConnectionService::new();
    
    // Add multiple connections
    let connection_ids: Vec<String> = (0..10).map(|_| service.add_connection()).collect();
    
    // Verify all connections exist
    for connection_id in &connection_ids {
        let connection = service.get_connection(connection_id);
        assert!(connection.is_some());
    }
    
    // Verify all connection IDs are unique
    let unique_ids: HashSet<String> = connection_ids.iter().cloned().collect();
    assert_eq!(unique_ids.len(), connection_ids.len());
}

#[test]
fn test_connection_state_new() {
    let state = ConnectionState::new();
    
    // Verify initial state
    assert_eq!(state.user_id, None);
    assert_eq!(state.session_id, None);
    assert_eq!(state.character_id, None);
    assert_eq!(state.character_list, None);
    assert!(state.additional_data.is_empty());
}

#[test]
fn test_connection_state_additional_data() {
    let mut state = ConnectionState::new();
    
    // Add some additional data
    state.additional_data.insert("key1".to_string(), "value1".to_string());
    state.additional_data.insert("key2".to_string(), "value2".to_string());
    
    // Verify the data was added
    assert_eq!(state.additional_data.get("key1"), Some(&"value1".to_string()));
    assert_eq!(state.additional_data.get("key2"), Some(&"value2".to_string()));
    
    // Update a value
    state.additional_data.insert("key1".to_string(), "updated".to_string());
    
    // Verify the value was updated
    assert_eq!(state.additional_data.get("key1"), Some(&"updated".to_string()));
}
