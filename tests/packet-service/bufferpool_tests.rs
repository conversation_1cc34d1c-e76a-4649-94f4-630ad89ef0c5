use packet_service::bufferpool::BufferPool;
use std::sync::Arc;

#[tokio::test]
async fn test_buffer_pool_creation() {
    let pool_size = 5;
    let pool = BufferPool::new(pool_size);
    
    // Verify we can acquire the expected number of buffers
    for _ in 0..pool_size {
        let buffer = pool.acquire().await;
        assert!(buffer.is_some());
    }
    
    // The next acquire should return None since all buffers are in use
    let buffer = pool.acquire().await;
    assert!(buffer.is_none());
}

#[tokio::test]
async fn test_buffer_pool_release() {
    let pool_size = 3;
    let pool = BufferPool::new(pool_size);
    
    // Acquire all buffers
    let mut buffers = Vec::new();
    for _ in 0..pool_size {
        let buffer = pool.acquire().await.unwrap();
        buffers.push(buffer);
    }
    
    // Release one buffer
    let buffer = buffers.pop().unwrap();
    pool.release(buffer).await;
    
    // We should be able to acquire one buffer now
    let buffer = pool.acquire().await;
    assert!(buffer.is_some());
    
    // But not two
    let buffer = pool.acquire().await;
    assert!(buffer.is_none());
}

#[tokio::test]
async fn test_buffer_pool_concurrent_access() {
    let pool_size = 10;
    let pool = Arc::new(BufferPool::new(pool_size));
    
    // Spawn multiple tasks to acquire and release buffers
    let mut handles = Vec::new();
    
    for i in 0..pool_size {
        let pool_clone = pool.clone();
        let handle = tokio::spawn(async move {
            // Acquire a buffer
            let mut buffer = pool_clone.acquire().await.unwrap();
            
            // Write some data to the buffer
            buffer[0] = i as u8;
            
            // Simulate some work
            tokio::time::sleep(tokio::time::Duration::from_millis(10)).await;
            
            // Release the buffer
            pool_clone.release(buffer).await;
        });
        
        handles.push(handle);
    }
    
    // Wait for all tasks to complete
    for handle in handles {
        handle.await.unwrap();
    }
    
    // All buffers should be available again
    for _ in 0..pool_size {
        let buffer = pool.acquire().await;
        assert!(buffer.is_some());
    }
}

#[tokio::test]
async fn test_buffer_pool_buffer_size() {
    let pool = BufferPool::new(1);
    
    // Acquire a buffer
    let buffer = pool.acquire().await.unwrap();
    
    // Buffer should be large enough for maximum packet size
    assert!(buffer.len() >= 0xFFF);
    
    // Release the buffer
    pool.release(buffer).await;
}
