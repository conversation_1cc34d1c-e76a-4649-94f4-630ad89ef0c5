#[tokio::test]
async fn test_redis_cache() {
    // dotenv().ok();
    // let redis_url = std::env::var("REDIS_URL").unwrap_or_else(|_| "redis://127.0.0.1:6379".to_string());
    // let cache = RedisCache::new(&redis_url);
    //
    // let key = &"test_key".to_string();
    // let value = "test_value";
    //
    // // Test setting a value
    // cache.set(key, &value, 10).await.unwrap();
    //
    // // Test getting the value
    // let cached_value: Option<String> = cache.get(key).await.unwrap();
    // assert_eq!(cached_value, Some("test_value".to_string()));
}
