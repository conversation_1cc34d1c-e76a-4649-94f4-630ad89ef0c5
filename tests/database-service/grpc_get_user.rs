use database_service::db::Database;
use database_service::grpc::database_service::MyDatabaseService;
use database_service::grpc::user_service_server::UserService;
use database_service::grpc::{GetUserRequest, GetUserResponse};
use sqlx::postgres::PgPoolOptions;
use std::sync::Arc;
use tokio::sync::Mutex;
use tonic::{Request, Response, Status};
use utils::redis_cache::RedisCache;

// Helper function to create a test database pool
async fn setup_test_pool() -> sqlx::PgPool {
    let database_url = std::env::var("TEST_DATABASE_URL")
        .unwrap_or_else(|_| "postgres://postgres:postgres@localhost:5432/test_db".to_string());

    PgPoolOptions::new()
        .max_connections(5)
        .connect(&database_url)
        .await
        .expect("Failed to create test database pool")
}

// Helper function to create a mock Redis cache
fn setup_test_cache() -> Arc<Mutex<RedisCache>> {
    let redis_url = std::env::var("TEST_REDIS_URL")
        .unwrap_or_else(|_| "redis://localhost:6379".to_string());

    Arc::new(Mutex::new(RedisCache::new(&redis_url)))
}

// Helper function to create a test user in the database
async fn create_test_user(pool: &sqlx::PgPool) -> i32 {
    let result = sqlx::query!(
        r#"
        INSERT INTO "user" (name, email, role, "createdAt", "updatedAt")
        VALUES ('test_user', '<EMAIL>', 'user', NOW(), NOW())
        RETURNING id
        "#
    )
    .fetch_one(pool)
    .await
    .expect("Failed to create test user");

    result.id
}

// Helper function to clean up test data
async fn cleanup_test_user(pool: &sqlx::PgPool, user_id: i32) {
    sqlx::query!(r#"DELETE FROM "user" WHERE id = $1"#, user_id)
        .execute(pool)
        .await
        .expect("Failed to delete test user");
}

#[tokio::test]
async fn test_grpc_get_user() {
    // Skip test if database connection is not available
    let pool_result = setup_test_pool().await;
    let pool = match pool_result {
        Ok(pool) => pool,
        Err(_) => {
            println!("Skipping test_grpc_get_user: Test database not available");
            return;
        }
    };

    let cache = setup_test_cache();

    // Create test user
    let user_id = create_test_user(&pool).await;

    // Create the database service
    let db = Arc::new(Database::new(pool.clone(), cache));
    let service = MyDatabaseService { db };

    // Create a gRPC request
    let request = Request::new(GetUserRequest {
        user_id,
    });

    // Call the service
    let response = service.get_user(request).await.unwrap().into_inner();

    // Validate the response
    assert_eq!(response.user_id, user_id);
    assert_eq!(response.username, "test_user");
    assert_eq!(response.email, "<EMAIL>");
    assert_eq!(response.role, "user");

    // Cleanup
    cleanup_test_user(&pool, user_id).await;
}
