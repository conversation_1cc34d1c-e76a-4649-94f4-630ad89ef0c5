use database_service::users::{User, UserRepository};
use sqlx::postgres::PgPoolOptions;
use std::sync::Arc;
use tokio::sync::Mutex;
use utils::redis_cache::RedisCache;

// Helper function to create a test database pool
async fn setup_test_pool() -> Result<sqlx::PgPool, sqlx::Error> {
    let database_url = std::env::var("TEST_DATABASE_URL")
        .unwrap_or_else(|_| "postgres://postgres:postgres@localhost:5432/test_db".to_string());

    PgPoolOptions::new()
        .max_connections(5)
        .connect(&database_url)
        .await
}

// Helper function to create a mock Redis cache
fn setup_test_cache() -> Arc<Mutex<RedisCache>> {
    let redis_url = std::env::var("TEST_REDIS_URL")
        .unwrap_or_else(|_| "redis://localhost:6379".to_string());

    Arc::new(Mutex::new(RedisCache::new(&redis_url)))
}

// Helper function to create a test user in the database
async fn create_test_user(pool: &sqlx::PgPool) -> i32 {
    let result = sqlx::query!(
        r#"
        INSERT INTO "user" (name, email, role, "createdAt", "updatedAt")
        VALUES ('test_user', '<EMAIL>', 'user', NOW(), NOW())
        RETURNING id
        "#
    )
    .fetch_one(pool)
    .await
    .expect("Failed to create test user");

    result.id
}

// Helper function to clean up test data
async fn cleanup_test_user(pool: &sqlx::PgPool, user_id: i32) {
    sqlx::query!(r#"DELETE FROM "user" WHERE id = $1"#, user_id)
        .execute(pool)
        .await
        .expect("Failed to delete test user");
}

#[tokio::test]
async fn test_get_user() {
    // Skip test if database connection is not available
    let pool = match setup_test_pool().await {
        Ok(pool) => pool,
        Err(_) => {
            println!("Skipping test_get_user: Test database not available");
            return;
        }
    };

    let cache = setup_test_cache();
    let repo = UserRepository::new(pool.clone(), cache);

    // Create test user
    let user_id = create_test_user(&pool).await;

    // Test the get_user_by_id function
    let user = repo.get_user_by_id(user_id).await.unwrap();

    // Validate the user
    assert_eq!(user.id, user_id);
    assert_eq!(user.name, "test_user");
    assert_eq!(user.email, "<EMAIL>");
    assert_eq!(user.role, "user");

    // Cleanup
    cleanup_test_user(&pool, user_id).await;
}
