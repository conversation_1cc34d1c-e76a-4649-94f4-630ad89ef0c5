use sqlx::postgres::PgPoolOptions;
use std::sync::Arc;
use tokio::sync::Mutex;
use utils::redis_cache::RedisCache;

// Helper function to create a test database pool
pub async fn setup_test_pool() -> Result<sqlx::PgPool, sqlx::Error> {
    let database_url = std::env::var("TEST_DATABASE_URL")
        .unwrap_or_else(|_| "postgres://postgres:postgres@localhost:5432/test_db".to_string());
    
    PgPoolOptions::new()
        .max_connections(5)
        .connect(&database_url)
        .await
}

// Helper function to create a mock Redis cache
pub fn setup_test_cache() -> Arc<Mutex<RedisCache>> {
    let redis_url = std::env::var("TEST_REDIS_URL")
        .unwrap_or_else(|_| "redis://localhost:6379".to_string());
    
    Arc::new(Mutex::new(RedisCache::new(&redis_url)))
}

// Helper function to create a test user in the database
pub async fn create_test_user(pool: &sqlx::PgPool, name: &str, email: &str) -> i32 {
    let result = sqlx::query!(
        r#"
        INSERT INTO "user" (name, email, role, "createdAt", "updatedAt")
        VALUES ($1, $2, 'user', NOW(), NOW())
        RETURNING id
        "#,
        name,
        email
    )
    .fetch_one(pool)
    .await
    .expect("Failed to create test user");

    result.id
}

// Helper function to create a test character in the database
pub async fn create_test_character(
    pool: &sqlx::PgPool, 
    user_id: i32, 
    name: &str
) -> i32 {
    let inventory = serde_json::json!({
        "items": [],
        "capacity": 100
    });
    
    let stats = serde_json::json!({
        "strength": 10,
        "dexterity": 10,
        "intelligence": 10,
        "vitality": 10
    });
    
    let skills = serde_json::json!({
        "skills": []
    });
    
    let looks = serde_json::json!({
        "race": 1,
        "gender": 0,
        "hair": 1,
        "face": 1
    });
    
    let position = serde_json::json!({
        "mapId": 1,
        "x": 100.0,
        "y": 100.0,
        "z": 0.0
    });

    let result = sqlx::query!(
        r#"
        INSERT INTO character (
            "userId", name, money, inventory, stats, skills, looks, position, 
            "createdAt", "updatedAt", "isActive"
        )
        VALUES ($1, $2, 0, $3, $4, $5, $6, $7, NOW(), NOW(), true)
        RETURNING id
        "#,
        user_id,
        name,
        inventory,
        stats,
        skills,
        looks,
        position
    )
    .fetch_one(pool)
    .await
    .expect("Failed to create test character");

    result.id
}

// Helper function to create a test session in the database
pub async fn create_test_session(pool: &sqlx::PgPool, user_id: i32) -> String {
    let session_id = uuid::Uuid::new_v4().to_string();
    
    sqlx::query!(
        r#"
        INSERT INTO session (id, "userId", "createdAt", "expiresAt")
        VALUES ($1, $2, NOW(), NOW() + INTERVAL '1 hour')
        "#,
        session_id,
        user_id
    )
    .execute(pool)
    .await
    .expect("Failed to create test session");

    session_id
}

// Helper function to clean up test user data
pub async fn cleanup_test_user(pool: &sqlx::PgPool, user_id: i32) {
    sqlx::query!(r#"DELETE FROM "user" WHERE id = $1"#, user_id)
        .execute(pool)
        .await
        .expect("Failed to delete test user");
}

// Helper function to clean up test character data
pub async fn cleanup_test_character(pool: &sqlx::PgPool, character_id: i32) {
    sqlx::query!(r#"DELETE FROM character WHERE id = $1"#, character_id)
        .execute(pool)
        .await
        .expect("Failed to delete test character");
}

// Helper function to clean up test session data
pub async fn cleanup_test_session(pool: &sqlx::PgPool, session_id: &str) {
    sqlx::query!(r#"DELETE FROM session WHERE id = $1"#, session_id)
        .execute(pool)
        .await
        .expect("Failed to delete test session");
}

// Helper function to clean up all test data
pub async fn cleanup_test_data(pool: &sqlx::PgPool, user_id: i32, character_id: i32, session_id: &str) {
    cleanup_test_session(pool, session_id).await;
    cleanup_test_character(pool, character_id).await;
    cleanup_test_user(pool, user_id).await;
}
