# MMORPG Server Tests

This directory contains tests for all components of the MMORPG server architecture.

## Running Tests

### Running All Tests

To run all tests:

```bash
cd tests
cargo test
```

### Running Tests for a Specific Service

To run tests for a specific service:

```bash
cd tests
cargo test --test auth_users_tests
cargo test --test character_service_tests
cargo test --test packet_tests
cargo test --test redis_cache_tests
cargo test --test get_user_tests
cargo test --test grpc_get_user_tests
cargo test --test mock_tests
# etc.
```

### Running Tests with Environment Variables

Some tests require environment variables to be set:

```bash
# Redis tests
export REDIS_TEST_ENABLED=true
export TEST_REDIS_URL=redis://127.0.0.1:6379

# Kubernetes tests
export KUBE_TEST_ENABLED=true
export TEST_K8S_SERVICE_NAME=database-service
export TEST_K8S_PORT_NAME=database-service

# Consul tests
export CONSUL_TEST_ENABLED=true
export TEST_CONSUL_URL=127.0.0.1:8600
export TEST_CONSUL_SERVICE_NAME=database-service

# Run tests
cd tests
cargo test
```

## Test Organization

Tests are organized by service:

- **auth-service/**: Tests for the authentication service
- **character-service/**: Tests for the character service
- **database-service/**: Tests for the database service
- **packet-service/**: Tests for the packet service
- **utils/**: Tests for shared utilities

## Adding New Tests

To add a new test:

1. Create a new test file in the appropriate service directory
2. Add the test to the `[[test]]` section in `Cargo.toml`
3. Run the test to ensure it works correctly
