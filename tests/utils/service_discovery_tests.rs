use std::env;
use std::net::SocketAddr;
use utils::service_discovery::{get_kube_service_endpoints_by_dns, get_service_endpoints_by_dns};

#[tokio::test]
async fn test_get_kube_service_endpoints_by_dns() {
    // Skip test if KUBE_TEST_ENABLED is not set to true
    if env::var("KUBE_TEST_ENABLED").unwrap_or_else(|_| "false".to_string()) != "true" {
        println!("Skipping Kubernetes DNS test. Set KUBE_TEST_ENABLED=true to run.");
        return;
    }

    // Test with a known Kubernetes service
    let service_name = env::var("TEST_K8S_SERVICE_NAME").unwrap_or_else(|_| "database-service".to_string());
    let port_name = env::var("TEST_K8S_PORT_NAME").unwrap_or_else(|_| "database-service".to_string());
    let protocol = "tcp";

    let result = get_kube_service_endpoints_by_dns(&port_name, protocol, &service_name).await;
    
    assert!(result.is_ok(), "Failed to get Kubernetes service endpoints: {:?}", result.err());
    
    let endpoints = result.unwrap();
    assert!(!endpoints.is_empty(), "No endpoints found for service {}", service_name);
    
    // Verify that the endpoints are valid socket addresses
    for endpoint in &endpoints {
        assert!(endpoint.port() > 0, "Invalid port in endpoint: {}", endpoint);
    }
    
    println!("Found {} endpoints for service {}: {:?}", endpoints.len(), service_name, endpoints);
}

#[tokio::test]
async fn test_get_service_endpoints_by_dns() {
    // Skip test if CONSUL_TEST_ENABLED is not set to true
    if env::var("CONSUL_TEST_ENABLED").unwrap_or_else(|_| "false".to_string()) != "true" {
        println!("Skipping Consul DNS test. Set CONSUL_TEST_ENABLED=true to run.");
        return;
    }

    // Test with a known Consul service
    let consul_url = env::var("TEST_CONSUL_URL").unwrap_or_else(|_| "127.0.0.1:8600".to_string());
    let service_name = env::var("TEST_CONSUL_SERVICE_NAME").unwrap_or_else(|_| "database-service".to_string());
    let protocol = "tcp";

    let result = get_service_endpoints_by_dns(&consul_url, protocol, &service_name).await;
    
    assert!(result.is_ok(), "Failed to get Consul service endpoints: {:?}", result.err());
    
    let endpoints = result.unwrap();
    assert!(!endpoints.is_empty(), "No endpoints found for service {}", service_name);
    
    // Verify that the endpoints are valid socket addresses
    for endpoint in &endpoints {
        assert!(endpoint.port() > 0, "Invalid port in endpoint: {}", endpoint);
    }
    
    println!("Found {} endpoints for service {}: {:?}", endpoints.len(), service_name, endpoints);
}

// Mock tests that don't require actual infrastructure
mod mock_tests {
    use std::net::{IpAddr, Ipv4Addr, SocketAddr};
    use std::str::FromStr;
    
    #[test]
    fn test_socket_addr_parsing() {
        // Test that we can parse socket addresses correctly
        let addr_str = "127.0.0.1:8080";
        let addr = SocketAddr::from_str(addr_str).unwrap();
        
        assert_eq!(addr.ip(), IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1)));
        assert_eq!(addr.port(), 8080);
    }
    
    #[test]
    fn test_socket_addr_formatting() {
        // Test that we can format socket addresses correctly
        let addr = SocketAddr::new(IpAddr::V4(Ipv4Addr::new(192, 168, 1, 1)), 9000);
        let addr_str = format!("{}", addr);
        
        assert_eq!(addr_str, "***********:9000");
    }
}
