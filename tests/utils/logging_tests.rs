use std::env;
use utils::logging::setup_logging;

#[test]
fn test_logging_setup() {
    // Test with default log level
    env::remove_var("LOG_LEVEL");
    setup_logging("test_app", &["test_crate"]);
    
    // Test with custom log level
    env::set_var("LOG_LEVEL", "debug");
    setup_logging("test_app", &["test_crate"]);
    
    // Test with invalid log level (should default to info)
    env::set_var("LOG_LEVEL", "invalid_level");
    setup_logging("test_app", &["test_crate"]);
    
    // Test with multiple additional crates
    setup_logging("test_app", &["test_crate1", "test_crate2", "test_crate3"]);
    
    // Clean up
    env::remove_var("LOG_LEVEL");
}

#[test]
fn test_logging_output() {
    // This test is more of a smoke test to ensure logging doesn't panic
    // Actual log output verification would require capturing stdout/stderr
    
    env::set_var("LOG_LEVEL", "trace");
    setup_logging("test_logging", &[]);
    
    // Log at different levels
    tracing::error!("This is an error message");
    tracing::warn!("This is a warning message");
    tracing::info!("This is an info message");
    tracing::debug!("This is a debug message");
    tracing::trace!("This is a trace message");
    
    // Clean up
    env::remove_var("LOG_LEVEL");
}
