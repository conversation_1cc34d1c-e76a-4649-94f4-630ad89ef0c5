use reqwest::StatusCode;
use std::env;
use std::time::Duration;
use tokio::time::sleep;
use utils::health_check::start_health_check;

#[tokio::test]
async fn test_health_check_endpoint() {
    // Set a custom port for this test to avoid conflicts
    env::set_var("HEALTH_CHECK_PORT", "8099");
    
    // Start the health check endpoint
    let result = start_health_check("127.0.0.1").await;
    assert!(result.is_ok(), "Failed to start health check: {:?}", result.err());
    
    // Give the server a moment to start
    sleep(Duration::from_millis(100)).await;
    
    // Make a request to the health check endpoint
    let client = reqwest::Client::new();
    let response = client.get("http://127.0.0.1:8099/health")
        .timeout(Duration::from_secs(2))
        .send()
        .await;
    
    // Verify the response
    assert!(response.is_ok(), "Failed to connect to health check endpoint: {:?}", response.err());
    let response = response.unwrap();
    assert_eq!(response.status(), StatusCode::OK);
    
    let body = response.text().await.unwrap();
    assert_eq!(body, "OK");
    
    // Clean up
    env::remove_var("HEALTH_CHECK_PORT");
}

#[tokio::test]
async fn test_health_check_invalid_path() {
    // Set a custom port for this test to avoid conflicts
    env::set_var("HEALTH_CHECK_PORT", "8098");
    
    // Start the health check endpoint
    let result = start_health_check("127.0.0.1").await;
    assert!(result.is_ok(), "Failed to start health check: {:?}", result.err());
    
    // Give the server a moment to start
    sleep(Duration::from_millis(100)).await;
    
    // Make a request to an invalid path
    let client = reqwest::Client::new();
    let response = client.get("http://127.0.0.1:8098/invalid")
        .timeout(Duration::from_secs(2))
        .send()
        .await;
    
    // Verify the response
    assert!(response.is_ok(), "Failed to connect to health check endpoint: {:?}", response.err());
    let response = response.unwrap();
    assert_eq!(response.status(), StatusCode::NOT_FOUND);
    
    // Clean up
    env::remove_var("HEALTH_CHECK_PORT");
}
