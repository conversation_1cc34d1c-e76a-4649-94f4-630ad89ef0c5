LOG_LEVEL=info
#REDIS_URL=redis://:uy~o5Sw%60ms4i%60saH%24yKMe9HDvPX2%60%26p3cV%23WFCP4V%26%409WZ2iLa2QKNUNQMmEWFa%40@************:6379/0
REDIS_URL=redis://cache:6379/0
LISTEN_ADDR=0.0.0.0
CONSUL_URL=http://consul:8500
CONSUL_HTTP_ADDR=http://consul:8500
CONSUL_HTTP_TOKEN=
CONSUL_ADDRESS=************
CONSUL_PORT=8500
CONSUL_DNS_PORT=8600
POSTGRES_USER=osirose
POSTGRES_PASSWORD=N7NYMjfj9XYZkavgV5zAUxrAvLkoUo4V
POSTGRES_DB=osirose
DATABASE_URL=***********************************************************/osirose

# Define service addresses for consul.
# This will set the service address in consul to what ever is placed here.
# This can be a IP address or the container name in docker
AUTH_SERVICE_ADDR=auth-service
API_SERVICE_ADDR=************
CHARACTER_SERVICE_ADDR=character-service
DATABASE_SERVICE_ADDR=database-service
PACKET_SERVICE_ADDR=************
WORLD_SERVICE_ADDR=world-service

# This will change the health check port for the services
#HEALTH_CHECK_PORT=8080