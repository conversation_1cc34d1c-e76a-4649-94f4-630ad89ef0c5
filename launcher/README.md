# Game Launcher

The Game Launcher is a client-side application that launches the MMORPG game client with the appropriate connection parameters.

## Overview

The Launcher is responsible for:
- Parsing launch URLs from the web authentication system
- Extracting connection parameters (server IP, port, session tokens)
- Launching the game client with the correct command-line arguments
- Handling updates (future functionality)

## URL Format

The launcher accepts URLs in the following format:

```
launcher://launch?ip=127.0.0.1&port=29000&session=SESSION_TOKEN&username=USERNAME
```

Parameters:
- `ip`: Game server IP address
- `port`: Game server port
- `otp`: One-time password (for direct login)
- `session`: Session token (for direct login)
- `username`: User's username
- `password`: User's password (only used for non-direct login)

## Command-Line Arguments

The launcher passes the following arguments to the game client:

- `@TRIGGER_SOFT@`: Required trigger argument
- `_server`: Server IP address
- `_port`: Server port
- `_direct`: Direct login flag
- `_otp`: One-time password
- `_session`: Session token
- `_userid`: User's username
- `_pass`: User's password

## Platform Support

The launcher supports:
- Windows: Launches TRose.exe directly
- Linux: Uses Bottles to run the Windows client

## Configuration

No additional configuration is required. The launcher extracts all necessary information from the launch URL.

## Building the Launcher

```bash
cargo build --release
```

## Usage

The launcher is typically invoked by clicking a link on the game's website:

```html
<a href="launcher://launch?ip=127.0.0.1&port=29000&session=SESSION_TOKEN&username=USERNAME">
  Launch Game
</a>
```

It can also be run directly from the command line:

```bash
launcher "launcher://launch?ip=127.0.0.1&port=29000&session=SESSION_TOKEN&username=USERNAME"
```

## Integration with External Systems

The Launcher integrates with:
- **Web Authentication System**: Receives launch parameters via URL
- **Game Client**: Launches the client with the appropriate parameters
