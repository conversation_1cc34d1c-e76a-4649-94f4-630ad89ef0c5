use crate::format_shell_command;
use std::borrow::Cow;
use std::process::{Command, Stdio};
use tracing::{debug, error, info, warn};
use url::Url;

#[cfg(target_os = "windows")]
fn create_command() -> Command {
    use crate::wait_for_keypress;
    use std::env;
    use std::process::exit;
    
    let exe_dir_path = env::current_exe().unwrap().parent().unwrap().to_path_buf();

    // Change the working directory
    if let Err(e) = env::set_current_dir(exe_dir_path) {
        error!("Failed to set working directory: {}", e);
        wait_for_keypress();
        exit(1);
    }

    let command = Command::new("./TRose.exe");
    command
}

#[cfg(target_os = "linux")]
fn create_command() -> Command {
    let mut command = Command::new("bottles-cli");
    command
        .arg("run")
        .arg("-p")
        .arg("TRose")
        .arg("-b")
        .arg("OsIRose")
        .arg("--args");
    command
}

pub(crate) fn launch_game(url: String) {
    // Parse the URL
    match Url::parse(&url) {
        Ok(parsed_url) => {
            let params = parsed_url.query_pairs();
            let mut command = create_command();
            command.arg("@TRIGGER_SOFT@");

            let mut is_direct = false;
            for (key, value) in params {
                debug!("Query pairs: [{}, {}]", key, value);

                match key {
                    Cow::Borrowed("ip") => {
                        command.arg("_server").arg(value.to_string());
                    }
                    Cow::Borrowed("port") => {
                        command.arg("_port").arg(value.to_string());
                    }
                    Cow::Borrowed("otp") => {
                        is_direct = true;
                        command.arg("_direct").arg("_otp").arg(value.to_string());
                    }
                    Cow::Borrowed("session") => {
                        is_direct = true;
                        command.arg("_direct").arg("_session").arg(value.to_string());
                    }
                    Cow::Borrowed("username") => {
                        command.arg("_userid").arg(value.to_string());
                    }
                    Cow::Borrowed("password") => {
                        if !is_direct {
                            command.arg("_pass").arg(value.to_string());
                        }
                    }
                    _ => {
                        warn!("Unexpected parameter: [{}, {}]", key, value);
                    }
                }
            }

            command.stdin(Stdio::null()).stdout(Stdio::null()).stderr(Stdio::null());

            info!("Executing: {:?}", format_shell_command(&command));

            // Check if the game launched successfully
            match command.spawn() {
                Ok(_) => info!("Game launched successfully!"),
                Err(e) => error!("Failed to launch the game: {}", e),
            }
        }
        Err(e) => error!("Failed to parse URL: {}", e),
    }
}
