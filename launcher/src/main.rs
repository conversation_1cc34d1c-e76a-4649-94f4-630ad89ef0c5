mod launcher;

use std::process::Command;
use std::{env, io};
use tracing::{debug, error, info, Level};
use url::Url;

#[derive(Debug)]
enum Commands {
    Launch,
    Update,
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    let verbose = 0;
    let verbose = match verbose {
        0 => Level::INFO,
        1 => Level::DEBUG,
        _ => Level::TRACE,
    };

    // Set our logging level
    tracing_subscriber::fmt().with_max_level(verbose).init();

    let args: Vec<String> = env::args().collect();
    if args.len() < 2 {
        error!("Usage: launcher <url>");
        return Err(std::io::Error::new(std::io::ErrorKind::InvalidInput, "url"))?;
    }

    let uri = &args[1];
    let url = Url::parse(uri)?;

    let method = if let Some(host) = url.host_str() {
        host
    } else if !url.path().is_empty() {
        url.path().trim_matches('/')
    } else {
        return Err("Method not specified in URL".into());
    };

    let action = match method {
        "launch" => Commands::Launch,
        "update" => Commands::Update,
        other => return Err(format!("Unknown method: {}", other).into()),
    };

    match action {
        Commands::Launch => {
            debug!("launching with URL {}", uri);
            launcher::launch_game(uri.to_string());
        }
        Commands::Update => {
            error!("Update action not implemented");
            return Err("Update action not implemented".into());
        }
    }

    Ok(())
}

fn wait_for_keypress() {
    // Wait for a keypress
    info!("Press Enter to close the launcher...");
    let _ = io::stdin().read_line(&mut String::new());
}

fn format_shell_command(command: &Command) -> String {
    let executable = command.get_program().to_string_lossy();
    let args: Vec<String> = command
        .get_args()
        .map(|arg| shell_escape::escape(arg.to_string_lossy()).into_owned())
        .collect();

    format!("{} {}", executable, args.join(" "))
}
