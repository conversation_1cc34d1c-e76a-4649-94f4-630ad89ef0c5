[package]
name = "auth-service"
version = "0.1.0"
edition = "2021"

[features]
mocks = []
consul = []

[dependencies]
tokio = { version = "1.41.1", features = ["full"] }
tonic = "0.12.3"
jsonwebtoken = "9.3.0"
argon2 = "0.5.3"
serde = { version = "1.0", features = ["derive"] }
dotenv = "0.15"
tracing = "0.1"
tracing-subscriber = { version = "0.3.19", features = ["env-filter", "chrono"] }
prost = "0.13.4"
prost-types = "0.13.3"
chrono = { version = "0.4.38", features = ["serde"] }
async-trait = "0.1.83"
mockall = "0.13.1"
rand = "0.8.5"
warp = "0.3.7"
reqwest = { version = "0.12.9", features = ["json"] }
utils = { path = "../utils" }
uuid = "1.11.0"
tonic-health = "0.12.3"

[build-dependencies]
tonic-build = "0.12.3"
