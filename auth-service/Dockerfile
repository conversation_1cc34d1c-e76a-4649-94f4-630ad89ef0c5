FROM rust:alpine AS builder
LABEL authors="raven"

RUN apk add --no-cache musl-dev libressl-dev protobuf-dev

WORKDIR /usr/src/utils
COPY ./utils .

WORKDIR /usr/src/proto
COPY ./proto .

# Set the working directory
WORKDIR /usr/src/auth-service

# Copy the project files
COPY ./auth-service .

# Build the application in release mode
RUN cargo build --release

# Use a minimal base image to run the application
FROM alpine:3

RUN apk add --no-cache libssl3 libgcc

# Copy the compiled binary
COPY --from=builder /usr/src/auth-service/target/release/auth-service /usr/local/bin/auth-service

# Expose the service port
EXPOSE 50051

# Set the entrypoint for the container
CMD ["auth-service"]