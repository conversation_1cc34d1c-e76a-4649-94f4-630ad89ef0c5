use crate::database::{
    user_service_client::UserServiceClient, GetUserByEmailRequest, GetUserByUsernameRequest, GetUserRequest,
    GetUserResponse,
};
use async_trait::async_trait;
use std::error::Error;
use tonic::transport::Channel;

#[async_trait]
pub trait DatabaseClientTrait: Sized {
    async fn connect(endpoint: &str) -> Result<Self, Box<dyn std::error::Error>>;
    async fn get_user_by_userid(&mut self, user_id: i32) -> Result<GetUserResponse, Box<dyn std::error::Error>>;
    async fn get_user_by_username(&mut self, user_id: &str) -> Result<GetUserResponse, Box<dyn std::error::Error>>;
    async fn get_user_by_email(&mut self, email: &str) -> Result<GetUserResponse, Box<dyn std::error::Error>>;
}
#[derive(Clone)]
pub struct DatabaseClient {
    client: UserServiceClient<Channel>,
}

#[async_trait]
impl DatabaseClientTrait for DatabaseClient {
    async fn connect(endpoint: &str) -> Result<Self, Box<dyn std::error::Error>> {
        let client = UserServiceClient::connect(endpoint.to_string()).await?;
        Ok(Self { client })
    }

    async fn get_user_by_userid(&mut self, user_id: i32) -> Result<GetUserResponse, Box<dyn std::error::Error>> {
        let request = tonic::Request::new(GetUserRequest { user_id });
        let response = self.client.get_user(request).await?;
        Ok(response.into_inner())
    }

    async fn get_user_by_username(&mut self, username: &str) -> Result<GetUserResponse, Box<dyn std::error::Error>> {
        let request = tonic::Request::new(GetUserByUsernameRequest {
            username: username.to_string(),
        });
        let response = self.client.get_user_by_username(request).await?;
        Ok(response.into_inner())
    }

    async fn get_user_by_email(&mut self, email: &str) -> Result<GetUserResponse, Box<dyn Error>> {
        let request = tonic::Request::new(GetUserByEmailRequest {
            email: email.to_string(),
        });
        let response = self.client.get_user_by_email(request).await?;
        Ok(response.into_inner())
    }
}
