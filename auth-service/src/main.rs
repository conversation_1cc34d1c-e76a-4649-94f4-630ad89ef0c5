use auth_service::auth::auth_service_server::AuthServiceServer;
use auth_service::database_client::DatabaseClient;
use auth_service::database_client::DatabaseClientTrait;
use auth_service::grpc::MyAuthService;
use auth_service::session::session_service_client::SessionServiceClient;
use dotenv::dotenv;
use std::env;
use std::sync::Arc;
use tonic::transport::Server;
use tracing::info;
use utils::logging;
use utils::service_discovery::get_kube_service_endpoints_by_dns;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Load environment variables from .env
    dotenv().ok();

    let app_name = env!("CARGO_PKG_NAME");
    logging::setup_logging(app_name, &["auth_service"]);

    // Set the gRPC server address
    let addr = env::var("LISTEN_ADDR").unwrap_or_else(|_| "0.0.0.0".to_string());
    let port = env::var("SERVICE_PORT").unwrap_or_else(|_| "50051".to_string());
    let db_url = format!(
        "http://{}",
        get_kube_service_endpoints_by_dns("database-service", "tcp", "database-service")
            .await?
            .get(0)
            .unwrap()
    );

    let db_client = Arc::new(DatabaseClient::connect(&db_url).await?);
    let session_client = Arc::new(SessionServiceClient::connect(db_url).await?);

    let full_addr = format!("{}:{}", &addr, port);
    let address = full_addr.parse().expect("Invalid address");
    let auth_service = MyAuthService {
        db_client,
        session_client,
    };

    let (mut health_reporter, health_service) = tonic_health::server::health_reporter();
    health_reporter.set_serving::<AuthServiceServer<MyAuthService>>().await;

    info!("Authentication Service running on {}", addr);

    // Start the gRPC server
    tokio::spawn(
        Server::builder()
            .add_service(health_service)
            .add_service(AuthServiceServer::new(auth_service))
            .serve(address),
    );

    utils::signal_handler::wait_for_signal().await;
    Ok(())
}
