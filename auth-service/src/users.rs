use crate::database::GetUserResponse;
use crate::database_client::DatabaseClientTrait;

use argon2::{
    password_hash::{rand_core::OsRng, PasswordHash, PasswordHasher, PasswordVerifier, SaltString},
    Argon2,
};

pub fn hash_password(password: &str) -> String {
    let salt = SaltString::generate(&mut OsRng);
    let argon2 = Argon2::default();
    argon2.hash_password(password.as_ref(), &salt).unwrap().to_string()
}

pub fn verify_password(password: &str, hash: &str) -> bool {
    let parsed_hash = PasswordHash::new(&hash).unwrap();
    Argon2::default()
        .verify_password(password.as_bytes(), &parsed_hash)
        .is_ok()
}

pub async fn verify_user<T: DatabaseClientTrait>(
    mut db_client: T,
    username: &str,
    password: &str,
) -> Option<GetUserResponse> {
    // let user = db_client.get_user_by_username(username).await.ok()?;
    //
    // if verify_password(password, &user.hashed_password) {
    //     Some(user)
    // } else {
    //     None
    // }
    None
}
