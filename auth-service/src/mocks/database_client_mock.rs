use crate::database::GetUserResponse;
use crate::database_client::DatabaseClientTrait;
use async_trait::async_trait;
use mockall::{mock, predicate::*};
use std::error::Error;

#[cfg(test)]
mock! {
    pub DatabaseClient {}

    #[async_trait]
    impl DatabaseClientTrait for DatabaseClient {
        async fn connect(endpoint: &str) -> Result<Self, Box<dyn std::error::Error>>;
        async fn get_user_by_userid(&mut self, user_id: i32) -> Result<GetUserResponse, Box<dyn std::error::Error>>;
        async fn get_user_by_username(&mut self, user_id: &str) -> Result<GetUserResponse, Box<dyn std::error::Error>>;
        async fn get_user_by_email(&mut self, email: &str) -> Result<GetUserResponse, Box<dyn Error>>;
    }
}

impl Clone for MockDatabaseClient {
    fn clone(&self) -> Self {
        MockDatabaseClient::new() // Create a new mock instance
    }
}
