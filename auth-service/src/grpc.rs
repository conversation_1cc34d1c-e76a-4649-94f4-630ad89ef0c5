use crate::auth::auth_service_server::AuthService;
use crate::auth::{
    LoginRequest, LoginResponse, LogoutRequest, RefreshSessionResponse, ValidateSessionRequest,
    ValidateSessionResponse, ValidateTokenRequest, ValidateTokenResponse,
};
use crate::common::Empty;
use crate::database_client::{DatabaseClient};
use crate::session::session_service_client::SessionServiceClient;
use crate::session::{GetSessionRequest, RefreshSessionRequest};
use std::sync::Arc;
use tonic::{Request, Response, Status};
use tracing::{debug, error, info, warn};

pub struct MyAuthService {
    pub db_client: Arc<DatabaseClient>,
    pub session_client: Arc<SessionServiceClient<tonic::transport::Channel>>,
}

#[tonic::async_trait]
impl AuthService for MyAuthService {
    async fn login(&self, _request: Request<LoginRequest>) -> Result<Response<LoginResponse>, Status> {
        Err(Status::unimplemented("login not implemented due to changes"))
    }

    async fn logout(&self, request: Request<LogoutRequest>) -> Result<Response<Empty>, Status> {
        let _req = request.into_inner();

        Ok(Response::new(Empty {}))
    }

    async fn validate_token(
        &self,
        _request: Request<ValidateTokenRequest>,
    ) -> Result<Response<ValidateTokenResponse>, Status> {
        Ok(Response::new(ValidateTokenResponse {
            valid: false,
            user_id: "".to_string(),
            session_id: "".to_string(),
        }))
    }

    async fn validate_session(
        &self,
        request: Request<ValidateSessionRequest>,
    ) -> Result<Response<ValidateSessionResponse>, Status> {
        let req = request.into_inner();
        let response = self
            .session_client
            .as_ref()
            .clone()
            .get_session(GetSessionRequest {
                session_id: req.session_id,
            })
            .await;

        match response {
            Ok(res) => {
                let res = res.into_inner();
                debug!("Session valid: {:?}", res);
                Ok(Response::new(ValidateSessionResponse {
                    valid: true,
                    session_id: res.session_id.to_string(),
                    user_id: res.user_id.to_string(),
                }))
            }
            Err(error) => {
                debug!("Session invalid or not found: {error}");
                Ok(Response::new(ValidateSessionResponse {
                    valid: false,
                    session_id: "".to_string(),
                    user_id: "".to_string(),
                }))
            }
        }
    }

    async fn refresh_session(
        &self,
        request: Request<ValidateSessionRequest>,
    ) -> Result<Response<RefreshSessionResponse>, Status> {
        let req = request.into_inner();
        let response = self
            .session_client
            .as_ref()
            .clone()
            .refresh_session(RefreshSessionRequest {
                session_id: req.session_id,
            })
            .await;

        match response {
            Ok(res) => {
                let res = res.into_inner();
                debug!("Session valid: {:?}", res);
                Ok(Response::new(RefreshSessionResponse { valid: res.valid }))
            }
            Err(_) => {
                debug!("Unable to refresh session");
                Ok(Response::new(RefreshSessionResponse { valid: false }))
            }
        }
    }
}
