use crate::session::{
    session_service_client::SessionServiceClient, GetSessionRequest, GetSessionResponse, RefreshSessionRequest,
    RefreshSessionResponse,
};
use async_trait::async_trait;
use std::error::Error;
use tonic::transport::Channel;

#[async_trait]
pub trait SessionClientTrait: Sized {
    async fn connect(endpoint: &str) -> Result<Self, Box<dyn std::error::Error>>;
    async fn get_session(&mut self, session_id: String) -> Result<GetSessionResponse, Box<dyn std::error::Error>>;
    async fn refresh_session(
        &mut self,
        session_id: String,
    ) -> Result<RefreshSessionResponse, Box<dyn std::error::Error>>;
}
#[derive(Clone)]
pub struct SessionClient {
    client: SessionServiceClient<Channel>,
}

#[async_trait]
impl SessionClientTrait for SessionClient {
    async fn connect(endpoint: &str) -> Result<Self, Box<dyn std::error::Error>> {
        let client = SessionServiceClient::connect(endpoint.to_string()).await?;
        Ok(Self { client })
    }

    async fn get_session(&mut self, session_id: String) -> Result<GetSessionResponse, Box<dyn Error>> {
        let request = tonic::Request::new(GetSessionRequest { session_id });
        let response = self.client.get_session(request).await?;
        Ok(response.into_inner())
    }
    async fn refresh_session(&mut self, session_id: String) -> Result<RefreshSessionResponse, Box<dyn Error>> {
        let request = tonic::Request::new(RefreshSessionRequest { session_id });
        let response = self.client.refresh_session(request).await?;
        Ok(response.into_inner())
    }
}
