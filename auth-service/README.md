# Authentication Service

The Authentication Service is responsible for user authentication, session validation, and account management in the MMORPG server architecture.

## Overview

The Authentication Service provides gRPC endpoints for:
- User login and logout
- Session validation and refresh

It communicates with the external database service to verify user credentials and manage sessions.

## Architecture

The service is built using the following components:

- **gRPC Server**: Exposes authentication endpoints
- **Database Client**: Communicates with the database service for user data
- **Session Client**: Manages user sessions
- **Password Hashing**: Securely handles password verification

> **Note**: User registration and password reset functionality are handled by an external system.

## Service Endpoints

The Authentication Service exposes the following gRPC endpoints:

### Login
Authenticates a user and creates a new session.

```protobuf
rpc Login(LoginRequest) returns (LoginResponse);
```

### Logout
Terminates a user session.

```protobuf
rpc Logout(LogoutRequest) returns (Empty);
```

### ValidateToken
Validates a JWT token.

```protobuf
rpc ValidateToken(ValidateTokenRequest) returns (ValidateTokenResponse);
```

### ValidateSession
Validates a session ID.

```protobuf
rpc ValidateSession(ValidateSessionRequest) returns (ValidateSessionResponse);
```

### RefreshSession
Refreshes an existing session.

```protobuf
rpc RefreshSession(ValidateSessionRequest) returns (RefreshSessionResponse);
```



## Configuration

The service can be configured using environment variables:

- `LISTEN_ADDR`: The address to listen on (default: "0.0.0.0")
- `SERVICE_PORT`: The port to listen on (default: "50051")
- `LOG_LEVEL`: Logging level (default: "info")

## Running the Service

### Local Development

```bash
cargo run
```

### Docker

```bash
docker build -t auth-service .
docker run -p 50051:50051 auth-service
```

## Integration with External Systems

The Authentication Service integrates with:

- **Database Service**: For user data storage and retrieval
- **Session Service**: For session management
- **External Auth System**: The service is designed to work with an external authentication system (better-auth) that manages the database schema and user accounts
