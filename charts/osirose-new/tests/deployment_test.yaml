suite: Test Deployment Template
templates:
  - deployment.yaml

tests:
  - it: should set the correct image
    values:
      services:
        - name: auth-service
          image: auth-service:latest
    asserts:
      - equal:
          path: spec.template.spec.containers[0].image
          value: my-docker-registry.com/my-project/auth-service:latest

  - it: should set the correct number of replicas
    values:
      services:
        - name: auth-service
          replicas: 3
    asserts:
      - equal:
          path: spec.replicas
          value: 3
