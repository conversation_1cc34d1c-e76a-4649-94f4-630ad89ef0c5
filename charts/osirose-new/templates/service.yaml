{{- range .Values.services }}
{{- if .service }}
apiVersion: v1
kind: Service
metadata:
  name: {{ .name }}
  labels:
    app: {{ .name }}
  {{- if .service.annotations }}
  annotations:
  {{- range $key, $value := .service.annotations }}
    {{ $key }}: "{{ $value }}"
  {{- end }}
  {{- end }}
spec:
  type: {{ .service.type | default "ClusterIP" }}
  ports:
    - name: {{ .service.portName | default "tcp" }}
      port: {{ .service.port }}
      targetPort: {{ .service.targetPort | default .service.port }}
      protocol: {{ .service.protocol | default "TCP" }}
  selector:
    app: {{ .name }}
{{- end }}
---
{{- end }}
