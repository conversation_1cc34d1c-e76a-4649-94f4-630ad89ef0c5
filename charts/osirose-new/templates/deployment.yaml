{{- range .Values.services }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .name }}
  labels:
    app: {{ .name }}
  {{- if .annotations }}
  annotations:
  {{- range $key, $value := .annotations }}
    {{ $key }}: "{{ $value }}"
  {{- end }}
  {{- end }}
spec:
  replicas: {{ .replicas }}
  selector:
    matchLabels:
      app: {{ .name }}
  template:
    metadata:
      labels:
        app: {{ .name }}
    spec:
      {{- if .serviceAccount }}
      serviceAccountName: {{ .serviceAccount }}
      {{- end }}
      containers:
        - name: {{ .name }}
          image: "{{ $.Values.repository }}/{{ .image }}"
          ports:
            - containerPort: {{ .port }}
          env:
            - name: DATABASE_URL
              value: "**************************************************************/$(POSTGRES_DB)"
          envFrom:
            - configMapRef:
                name: {{ .name }}-env
            - secretRef:
                name: postgres-secrets
---
{{- end }}
