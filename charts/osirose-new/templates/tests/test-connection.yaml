{{- if .Values.tests.enabled }}
{{- range .Values.tests.services }}
apiVersion: v1
kind: Pod
metadata:
  name: {{ .name }}-test
  annotations:
    "helm.sh/hook": test
    "helm.sh/hook-delete-policy": before-hook-creation
  labels:
    app: {{ .name }}
spec:
  containers:
    - name: {{ .name }}-test
      image: {{ .image }}
      command: {{ .testCommand }}
  restartPolicy: Never
---
{{- end }}
{{- end }}
