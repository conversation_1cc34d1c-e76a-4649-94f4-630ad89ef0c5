{{- range .Values.services }}
{{- if .ingress }}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ .name }}-ingress
  labels:
    app: {{ .name }}
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
spec:
  rules:
    - host: {{ .ingress.hostname }}
      http:
        paths:
          - path: {{ .ingress.path }}
            pathType: Prefix
            backend:
              service:
                name: {{ .name }}
                port:
                  number: {{ .port }}
---
{{- end }}
{{- end }}
