FROM rust:alpine AS builder
LABEL authors="raven"

RUN apk add --no-cache musl-dev libressl-dev protobuf-dev

WORKDIR /usr/src/utils
COPY ./utils .

WORKDIR /usr/src/proto
COPY ./proto .

WORKDIR /usr/src/game-logic-service
COPY ./game-logic-service .

RUN cargo build --release

FROM alpine:3

RUN apk add --no-cache libssl3 libgcc

COPY --from=builder /usr/src/game-logic-service/target/release/game-logic-service /usr/local/bin/game-logic-service

EXPOSE 50056

CMD ["game-logic-service"]