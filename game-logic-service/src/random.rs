use rand::Rng;

pub fn get_random_number() -> u32 {
    rand::thread_rng().gen_range(0..1000000)
}

pub fn get_random_number_in_range(min: u32, max: u32) -> u32 {
    rand::thread_rng().gen_range(min..=max)
}

pub fn get_random_number_in_range_f32(min: f32, max: f32) -> f32 {
    rand::thread_rng().gen_range(min..=max)
}

pub fn get_random_number_in_range_f64(min: f64, max: f64) -> f64 {
    rand::thread_rng().gen_range(min..=max)
}

pub fn get_random_bool() -> bool {
    rand::thread_rng().gen_bool(0.5)
}

pub fn get_random_item_from_vec<T>(vec: &Vec<T>) -> Option<&T> {
    if vec.is_empty() {
        return None;
    }
    let index = rand::thread_rng().gen_range(0..vec.len());
    vec.get(index)
}

pub fn get_random_point_in_circle(center: (f32, f32), radius: f32) -> (f32, f32) {
    let angle = rand::thread_rng().gen_range(0.0..std::f32::consts::PI * 2.0);
    let distance = rand::thread_rng().gen_range(0.0..radius);
    let x = center.0 + distance * angle.cos();
    let y = center.1 + distance * angle.sin();
    (x, y)
}
