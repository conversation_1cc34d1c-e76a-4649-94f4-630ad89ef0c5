use std::pin::Pin;
use futures::{Stream, StreamExt};
use tonic::{Request, Response, Status, Streaming};
use tracing::{info, error};
use tokio_stream::wrappers::ReceiverStream;
use tokio::sync::mpsc;

pub mod character_common {
    tonic::include_proto!("character_common");
}
pub mod game {
    tonic::include_proto!("game");
}

use game::event_service_server::{EventService, EventServiceServer};
use game::GenericEvent;

pub struct MyGameService {}

impl MyGameService {
    pub fn into_service(self) -> EventServiceServer<Self> {
        EventServiceServer::new(self)
    }
}

#[tonic::async_trait]
impl EventService for MyGameService {
    type StreamEventsStream =
    Pin<Box<dyn Stream<Item = Result<GenericEvent, Status>> + Send + Sync + 'static>>;

    async fn stream_events(
        &self,
        request: Request<Streaming<GenericEvent>>,
    ) -> Result<Response<Self::StreamEventsStream>, Status> {
        info!("Received connection from world service without authentication");

        // Extract the inbound stream.
        let mut inbound_stream = request.into_inner();

        // Create a channel for sending outgoing events.
        let (tx, rx) = mpsc::channel(32);

        // Spawn a task to handle processing of inbound events.
        let tx_clone = tx.clone();
        tokio::spawn(async move {
            while let Some(event) = inbound_stream.next().await {
                match event {
                    Ok(ev) => {
                        info!("Received event: {:?}", ev);
                        // Process the event as needed.
                        // For demonstration, simply echo the event back.
                        if let Err(err) = tx_clone.send(ev).await {
                            error!("Failed forwarding event: {:?}", err);
                        }
                    }
                    Err(e) => {
                        error!("Error receiving event: {:?}", e);
                        break;
                    }
                }
            }
            info!("Inbound event stream ended.");
        });

        // Wrap the receiver in a stream and return it.
        let outbound_stream = ReceiverStream::new(rx).map(|msg| Ok(msg));
        Ok(Response::new(Box::pin(outbound_stream) as Self::StreamEventsStream))
    }
}