use std::collections::HashSet;

#[derive(<PERSON><PERSON>, Debug)]
pub struct IdManager {
    free_ids: HashSet<u16>,
    max_id: u16, // the first ID is 1
}

impl IdManager {
    /// Creates a new IdManager with no free IDs and starting ID of 1.
    pub fn new() -> Self {
        Self {
            free_ids: HashSet::new(),
            max_id: 1,
        }
    }

    /// Retrieves an available ID.
    ///
    /// If any are available in the free_ids set, returns one of them.
    /// Otherwise, returns a fresh ID and increments max_id.
    pub fn get_free_id(&mut self) -> u16 {
        if let Some(&id) = self.free_ids.iter().next() {
            self.free_ids.remove(&id);
            id
        } else {
            let id = self.max_id;
            self.max_id += 1;
            id
        }
    }

    /// Releases an ID, making it available for reuse.
    pub fn release_id(&mut self, id: u16) {
        self.free_ids.insert(id);
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_id_manager() {
        let mut manager = IdManager::new();

        let id1 = manager.get_free_id();
        let id2 = manager.get_free_id();
        assert_eq!(id1, 1);
        assert_eq!(id2, 2);

        // Release id1 and then get a free id which should return id1
        manager.release_id(id1);
        let id3 = manager.get_free_id();
        assert_eq!(id3, id1);

        // Next free id should be id3 (old id2 was already used)
        let id4 = manager.get_free_id();
        assert_eq!(id4, 3);
    }
}