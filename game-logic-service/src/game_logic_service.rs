use futures::{Stream, StreamExt};
use std::collections::HashMap;
use std::pin::Pin;
use std::sync::{Arc, Mutex};
use tonic::{Request, Response, Status};
use tonic::metadata::MetadataMap;
use tracing::debug;

pub mod game_logic {
    tonic::include_proto!("game_logic");
}

use game_logic::game_logic_service_server::GameLogicService;
use crate::game_logic_service::game_logic::{NearbyObjectsRequest, NearbyObjectsResponse};

pub struct MyGameLogicService {
    pub map_id: u32,
}

#[tonic::async_trait]
impl GameLogicService for MyGameLogicService {
    async fn get_nearby_objects(&self, request: Request<NearbyObjectsRequest>) -> Result<Response<NearbyObjectsResponse>, Status> {
        let req = request.into_inner();
        debug!("{:?}", req);

        let response = NearbyObjectsResponse {
            objects: vec![],
        };
        Ok(Response::new(response))
    }
}