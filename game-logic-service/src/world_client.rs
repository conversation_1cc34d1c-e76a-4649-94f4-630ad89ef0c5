use std::sync::Arc;
use tokio::sync::{mpsc, Mutex};
use tonic::{Request, Response, Status, Streaming};
use tracing::{debug, error, info, warn};
use futures::{Stream, StreamExt};
use std::pin::Pin;

pub mod world {
    tonic::include_proto!("world");
}

use world::world_game_logic_service_server::{WorldGameLogicService};

pub use world::world_game_logic_service_server::WorldGameLogicServiceServer;

pub struct WorldGameLogicServiceImpl {
    map_id: u32,
    event_sender: Arc<Mutex<Option<mpsc::UnboundedSender<world::GameLogicEvent>>>>,
    event_receiver: Arc<Mutex<Option<mpsc::UnboundedReceiver<world::GameLogicEvent>>>>,
}

impl WorldGameLogicServiceImpl {
    pub fn new(map_id: u32) -> Self {
        let (tx, rx) = mpsc::unbounded_channel();
        Self {
            map_id,
            event_sender: Arc::new(Mutex::new(Some(tx))),
            event_receiver: Arc::new(Mutex::new(Some(rx))),
        }
    }

    pub async fn send_event(&self, event: world::GameLogicEvent) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let sender = self.event_sender.lock().await;
        if let Some(tx) = sender.as_ref() {
            tx.send(event)?;
            Ok(())
        } else {
            Err("Event sender not available".into())
        }
    }
}

#[tonic::async_trait]
impl WorldGameLogicService for WorldGameLogicServiceImpl {
    type StreamGameEventsStream = Pin<Box<dyn Stream<Item = Result<world::GameLogicEvent, Status>> + Send + Sync + 'static>>;

    async fn stream_game_events(
        &self,
        request: Request<Streaming<world::GameLogicEvent>>,
    ) -> Result<Response<Self::StreamGameEventsStream>, Status> {
        info!("World service connected to game logic for map {}", self.map_id);

        let mut inbound_stream = request.into_inner();
        let (outbound_tx, outbound_rx) = mpsc::unbounded_channel();

        let map_id = self.map_id;

        // Handle incoming events from world service
        tokio::spawn(async move {
            while let Some(event) = inbound_stream.next().await {
                match event {
                    Ok(game_event) => {
                        debug!("Received event from world service for map {}: {:?}", map_id, game_event);

                        // Process the event based on its type
                        match game_event.event {
                            Some(world::game_logic_event::Event::PlayerConnect(connect_event)) => {
                                info!("Player {} connected to map {}", connect_event.session_id, map_id);
                                // Handle player connection logic
                            }
                            Some(world::game_logic_event::Event::PlayerDisconnect(disconnect_event)) => {
                                info!("Player {} disconnected from map {}", disconnect_event.session_id, map_id);
                                // Handle player disconnection logic
                            }
                            Some(world::game_logic_event::Event::PlayerMove(move_event)) => {
                                debug!("Player {} moved to ({}, {}, {}) in map {}",
                                       move_event.session_id, move_event.x, move_event.y, move_event.z, map_id);
                                // Handle player movement logic
                            }
                            _ => {
                                debug!("Unhandled event type from world service");
                            }
                        }
                    }
                    Err(e) => {
                        error!("Error receiving event from world service for map {}: {}", map_id, e);
                        break;
                    }
                }
            }
            info!("World service stream ended for map {}", map_id);
        });

        // Create outbound stream for sending events to world service
        let outbound_stream = tokio_stream::wrappers::UnboundedReceiverStream::new(outbound_rx)
            .map(|event| Ok(event));

        Ok(Response::new(Box::pin(outbound_stream) as Self::StreamGameEventsStream))
    }
}

impl WorldGameLogicServiceImpl {
    pub fn create_mob_spawn_event(&self, mob_id: u32, x: f32, y: f32, npc_id: u32) -> world::GameLogicEvent {
        world::GameLogicEvent {
            client_ids: vec![], // Will be populated by world service based on nearby players
            map_id: self.map_id as i32,
            event: Some(world::game_logic_event::Event::MobSpawn(world::MobSpawnEvent {
                id: mob_id,
                pos_x: x,
                pos_y: y,
                dest_pos_x: x,
                dest_pos_y: y,
                command: 0,
                target_id: 0,
                move_mode: 0,
                hp: 100,
                team_id: 0,
                status_flag: 0,
                npc_id,
                quest_id: 0,
            })),
        }
    }

    pub fn create_npc_spawn_event(&self, npc_id: u32, x: f32, y: f32, angle: f32) -> world::GameLogicEvent {
        world::GameLogicEvent {
            client_ids: vec![], // Will be populated by world service based on nearby players
            map_id: self.map_id as i32,
            event: Some(world::game_logic_event::Event::NpcSpawn(world::NpcSpawnEvent {
                id: npc_id,
                pos_x: x,
                pos_y: y,
                dest_pos_x: x,
                dest_pos_y: y,
                command: 0,
                target_id: 0,
                move_mode: 0,
                hp: 100,
                team_id: 0,
                status_flag: 0,
                npc_id,
                quest_id: 0,
                angle,
                event_status: 0,
            })),
        }
    }

    pub fn create_object_despawn_event(&self, object_id: u32) -> world::GameLogicEvent {
        world::GameLogicEvent {
            client_ids: vec![], // Will be populated by world service based on nearby players
            map_id: self.map_id as i32,
            event: Some(world::game_logic_event::Event::ObjectDespawn(world::ObjectDespawnEvent {
                object_id,
            })),
        }
    }
}
