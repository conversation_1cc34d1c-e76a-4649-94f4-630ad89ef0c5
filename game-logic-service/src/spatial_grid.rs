use std::collections::{HashMap, HashSet};
use hecs::{Entity, World};
use crate::components::position::Position;
use crate::components::markers::*;
use crate::components::basic_info::BasicInfo;

/// Grid size in world units - each grid cell represents 1000x1000 world units
const GRID_SIZE: f32 = 1000.0;

/// Maximum search radius in grid cells (equivalent to C++ implementation)
const MAX_SEARCH_RADIUS: i16 = 10;

/// Grid coordinate type
type GridCoord = (i16, i16);

/// Spatial grid for efficient nearby entity queries
#[derive(Debug)]
pub struct SpatialGrid {
    /// Grid cells containing sets of entities
    grid: HashMap<GridCoord, HashSet<Entity>>,
}

impl SpatialGrid {
    pub fn new() -> Self {
        Self {
            grid: HashMap::new(),
        }
    }

    /// Convert world coordinates to grid coordinates
    fn world_to_grid(x: f32, y: f32) -> GridCoord {
        let gx = (x / GRID_SIZE) as i16;
        let gy = (y / GRID_SIZE) as i16;
        (gx, gy)
    }

    /// Add an entity to the spatial grid
    pub fn add_entity(&mut self, entity: Entity, position: &Position) {
        let grid_pos = Self::world_to_grid(position.x, position.y);
        self.grid.entry(grid_pos).or_insert_with(HashSet::new).insert(entity);
    }

    /// Remove an entity from the spatial grid
    pub fn remove_entity(&mut self, entity: Entity, position: &Position) {
        let grid_pos = Self::world_to_grid(position.x, position.y);
        if let Some(cell) = self.grid.get_mut(&grid_pos) {
            cell.remove(&entity);
            // Clean up empty cells
            if cell.is_empty() {
                self.grid.remove(&grid_pos);
            }
        }
    }

    /// Update an entity's position in the grid
    pub fn update_entity_position(&mut self, entity: Entity, old_position: &Position, new_position: &Position) {
        let old_grid_pos = Self::world_to_grid(old_position.x, old_position.y);
        let new_grid_pos = Self::world_to_grid(new_position.x, new_position.y);
        
        // Only update if the grid position actually changed
        if old_grid_pos != new_grid_pos {
            self.remove_entity(entity, old_position);
            self.add_entity(entity, new_position);
        }
    }

    /// Check if two entities are nearby (within MAX_SEARCH_RADIUS grid cells)
    pub fn is_nearby(&self, pos1: &Position, pos2: &Position) -> bool {
        let grid_pos1 = Self::world_to_grid(pos1.x, pos1.y);
        let grid_pos2 = Self::world_to_grid(pos2.x, pos2.y);
        
        let dx = (grid_pos1.0 - grid_pos2.0).abs();
        let dy = (grid_pos1.1 - grid_pos2.1).abs();
        
        dx <= MAX_SEARCH_RADIUS && dy <= MAX_SEARCH_RADIUS
    }

    /// Get all entities near the given position
    /// Returns entities within MAX_SEARCH_RADIUS grid cells, excluding the query entity itself
    pub fn get_nearby_entities(&self, world: &World, query_entity: Option<Entity>, position: &Position, map_id: u16) -> Vec<Entity> {
        let center_grid = Self::world_to_grid(position.x, position.y);
        let mut nearby_entities = HashSet::new();

        // Search in a square around the center position
        for x in (center_grid.0 - MAX_SEARCH_RADIUS)..=(center_grid.0 + MAX_SEARCH_RADIUS) {
            for y in (center_grid.1 - MAX_SEARCH_RADIUS)..=(center_grid.1 + MAX_SEARCH_RADIUS) {
                if let Some(cell) = self.grid.get(&(x, y)) {
                    for &entity in cell {
                        // Skip the query entity itself
                        if let Some(query_ent) = query_entity {
                            if entity == query_ent {
                                continue;
                            }
                        }

                        // Verify the entity is still valid and on the same map
                        if let Ok(entity_pos) = world.get::<&Position>(entity) {
                            if entity_pos.map_id == map_id {
                                nearby_entities.insert(entity);
                            }
                        }
                    }
                }
            }
        }

        nearby_entities.into_iter().collect()
    }

    /// Get nearby entities within a specific radius (in world units)
    pub fn get_nearby_entities_within_radius(&self, world: &World, query_entity: Option<Entity>, position: &Position, map_id: u16, radius: f32) -> Vec<Entity> {
        let nearby = self.get_nearby_entities(world, query_entity, position, map_id);
        let radius_squared = radius * radius;

        nearby.into_iter().filter(|&entity| {
            if let Ok(entity_pos) = world.get::<&Position>(entity) {
                let dx = entity_pos.x - position.x;
                let dy = entity_pos.y - position.y;
                let distance_squared = dx * dx + dy * dy;
                distance_squared <= radius_squared
            } else {
                false
            }
        }).collect()
    }

    /// Rebuild the entire spatial grid from the world state
    /// This should be called periodically to clean up invalid entities
    pub fn rebuild_from_world(&mut self, world: &World) {
        self.grid.clear();
        
        // Add all entities with positions to the grid
        for (entity, position) in world.query::<&Position>().iter() {
            self.add_entity(entity, position);
        }
    }

    /// Get statistics about the spatial grid
    pub fn get_stats(&self) -> (usize, usize) {
        let cell_count = self.grid.len();
        let entity_count = self.grid.values().map(|cell| cell.len()).sum();
        (cell_count, entity_count)
    }
}

impl Default for SpatialGrid {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use hecs::World;

    #[test]
    fn test_world_to_grid() {
        assert_eq!(SpatialGrid::world_to_grid(0.0, 0.0), (0, 0));
        assert_eq!(SpatialGrid::world_to_grid(999.0, 999.0), (0, 0));
        assert_eq!(SpatialGrid::world_to_grid(1000.0, 1000.0), (1, 1));
        assert_eq!(SpatialGrid::world_to_grid(-1000.0, -1000.0), (-1, -1));
    }

    #[test]
    fn test_add_remove_entity() {
        let mut grid = SpatialGrid::new();
        let mut world = World::new();
        
        let pos = Position { x: 500.0, y: 500.0, z: 0.0, map_id: 1, spawn_id: 0 };
        let entity = world.spawn((pos.clone(),));
        
        grid.add_entity(entity, &pos);
        assert_eq!(grid.grid.len(), 1);
        
        grid.remove_entity(entity, &pos);
        assert_eq!(grid.grid.len(), 0);
    }
}
