use std::cmp::min;
use hecs::{Entity, World};
use std::time::{Duration, Instant};
use std::sync::{Arc, Mutex};
use tracing::debug;
use crate::components::basic_info::BasicInfo;
use crate::components::level::Level;
use crate::components::life::Life;
use crate::components::position::Position;
use crate::components::spawner::Spawner;
use crate::components::markers::*;
use crate::id_manager::IdManager;
use crate::loader::{MobSpawnPoint, Vec3, Zone};
use crate::random::{get_random_number_in_range, get_random_point_in_circle};
use crate::spatial_grid::SpatialGrid;

pub struct EntityFactory<'a> {
    pub world: &'a mut World,
    pub id_manager: Arc<Mutex<IdManager>>,
    pub spatial_grid: SpatialGrid,
}

impl<'a> EntityFactory<'a> {
    /// Creates a new factory.
    pub fn new(world: &'a mut World) -> Self {
        Self {
            world,
            id_manager: Arc::new(Mutex::new(IdManager::new())),
            spatial_grid: SpatialGrid::new(),
        }
    }
    
    pub fn load_map(&mut self, map: Zone) {
        // Load the map data from the database
        // Spawn all the entities in the map

        if let Some(mob_spawn_points) = map.mob_spawn_points {
            for spawner in mob_spawn_points {
                // Process the mob spawner.
                spawner
                    .normal_spawn_points
                    .unwrap_or_default()
                    .into_iter()
                    .chain(spawner.tactical_spawn_points.unwrap_or_default().into_iter())
                    .for_each(|spawn_point| {
                        // debug!("Spawn Point: {:?}", spawn_point);
                        self.spawn_mob_spawner(
                            spawn_point.monster_id,
                            spawner.limit,
                            spawn_point.count,
                            Position {
                                x: spawner.position.x as f32,
                                y: spawner.position.y as f32,
                                z: spawner.position.z as f32,
                                map_id: map.id as u16,
                                spawn_id: 0,
                            },
                            Duration::from_secs(spawner.interval as u64),
                            spawner.range,
                        );
                    });
            }
        }

        if let Some(npc_spawn_points) = map.npc_spawn_points {
            for npc in npc_spawn_points {
                // Process the npc spawn point.
                // debug!("NPC ID: {}", npc.id);
                // debug!("Dialog ID: {}", npc.dialog_id);
                // debug!("Position: x = {}, y = {}, z = {}",
                //          npc.position.x, npc.position.y, npc.position.z);
                self.spawn_npc(npc.id, Position {
                    x: npc.position.x as f32,
                    y: npc.position.y as f32,
                    z: npc.position.z as f32,
                    map_id: map.id as u16,
                    spawn_id: 0,
                },
                npc.angle);
            }
        }

        if let Some(spawn_points) = map.spawn_points {
            for spawn_point in spawn_points {
                // Process the spawn point.
                // debug!("Player Spawn Point Type: {}", spawn_point.point_type);
                // debug!("Position: x = {}, y = {}, z = {}",
                //          spawn_point.position.x, spawn_point.position.y, spawn_point.position.z);
                self.create_player_spawn_point(spawn_point.point_type, Position {
                    x: spawn_point.position.x as f32,
                    y: spawn_point.position.y as f32,
                    z: spawn_point.position.z as f32,
                    map_id: map.id as u16,
                    spawn_id: 0,
                });
            }
        }

        if let Some(warp_points) = map.warp_points {
            for warp_point in warp_points {
                // Process the warp point.
                // debug!("Warp Point Alias: {}", warp_point.alias);
                // debug!("Destination Gate ID: {}", warp_point.destination_gate_id);
                // debug!("Destination: x = {}, y = {}, z = {}",
                //          warp_point.destination.x, warp_point.destination.y, warp_point.destination.z);
                // debug!("Map ID: {}", warp_point.map_id);
                // debug!("Min Position: x = {}, y = {}, z = {}",
                //          warp_point.min_position.x, warp_point.min_position.y, warp_point.min_position.z);
                // debug!("Max Position: x = {}, y = {}, z = {}",
                //          warp_point.max_position.x, warp_point.max_position.y, warp_point.max_position.z);
            }
        }
    }
    
    pub fn create_player_spawn_point(&mut self, point_type: u32, pos: Position) {
        self.world.spawn((PlayerSpawn {point_type},pos.clone()));
        debug!("Player spawn point created at position: {:?}", pos);
    }
    
    pub fn spawn_player(&mut self, pos: Position) {
        let id = self.id_manager.lock().unwrap().get_free_id();
        let basic_info = BasicInfo {
            name: "Player".to_string(),
            id: id,
            tag: id as u32,
            ..Default::default()
        };
        let level = Level { level: 1, ..Default::default() };
        
        let base_hp = 100;
        let hp = (base_hp * level.level) as u32;
        let life = Life::new(hp, hp);
        
        let entity = self.world.spawn((Player, basic_info, level, life, pos.clone()));

        // Add entity to spatial grid
        self.spatial_grid.add_entity(entity, &pos);

        debug!("Player spawned at position: {:?}", pos);
    }

    /// Spawns a npc at the specified position.
    pub fn spawn_npc(&mut self, npc_id: u32, pos: Position, angle: f32) {
        let id = self.id_manager.lock().unwrap().get_free_id();
        let basic_info = BasicInfo {
            name: "NPC".to_string(),
            id: id,
            tag: id as u32,
            ..Default::default()
        };
        let level = Level { level: 1, ..Default::default() };
        
        let base_hp = 100;
        let hp = (base_hp * level.level) as u32;
        let life = Life::new(hp, hp);
        
        let entity = self.world.spawn((Npc {id: npc_id, quest_id: 0, angle, event_status: 0}, basic_info, level, life, pos.clone()));

        // Add entity to spatial grid
        self.spatial_grid.add_entity(entity, &pos);

        debug!("Npc spawned at position: {:?}", pos);
    }

    /// Spawns a mob at the specified position.
    pub fn spawn_mob(&mut self, mob_id: u32, spawn_range: u32, pos: Position) -> Entity {
        let id = self.id_manager.lock().unwrap().get_free_id();
        let basic_info = BasicInfo {
            name: "MOB".to_string(),
            id: id,
            tag: id as u32,
            ..Default::default()
        };

        let level = Level { level: 1, ..Default::default() };

        let base_hp = 100;
        let hp = (base_hp * level.level) as u32;
        let life = Life::new(hp, hp);

        let (x, y) = get_random_point_in_circle((pos.x, pos.y), spawn_range as f32);
        let spawn_point = Position { x, y, z: pos.z, map_id: pos.map_id, spawn_id: pos.spawn_id };

        // Spawn the mob.
        let entity = self.world.spawn((Mob {id: mob_id, quest_id: 0}, basic_info, spawn_point.clone()));

        // Add entity to spatial grid
        self.spatial_grid.add_entity(entity, &spawn_point);

        entity
    }

    /// Spawns a spawner at the specified position with the given spawn rate.
    pub fn spawn_mob_spawner(&mut self, mob_id: u32, max_mob_count: u32, max_spawn_count_at_once: u32, pos: Position, spawn_rate: Duration, spawn_range: u32) {
        let spawner = Spawner {
            mob_id,
            spawn_rate,
            spawn_range,
            max_mob_count,
            max_spawn_count_at_once,
            ..Default::default()
        };
        self.world.spawn((spawner, pos.clone()));
    }

    /// Updates all spawner entities in the world.
    ///
    /// If the spawn interval has elapsed, a mob will be spawned and the spawner's
    /// last spawn timestamp is updated.
    pub fn update_spawners(&mut self) {
        let now = Instant::now();

        // Collect spawner entities to avoid borrow issues.
        // We need to clone the Position since we use it after.
        let spawner_data: Vec<(hecs::Entity, Position, Spawner)> = self
            .world
            .query::<(&Position, &Spawner)>()
            .iter()
            .map(|(entity, (pos, spawner))| {
                (
                    entity,
                    pos.clone(),
                    Spawner {
                        mob_id: spawner.mob_id,
                        max_mob_count: spawner.max_mob_count,
                        max_spawn_count_at_once: spawner.max_spawn_count_at_once,
                        spawn_rate: spawner.spawn_rate,
                        last_spawn: spawner.last_spawn,
                        spawn_range: spawner.spawn_range,
                        mobs: spawner.mobs.clone(),
                        ..Default::default()
                    },
                )
            })
            .collect();

        // Iterate over each spawner and check if it's time to spawn a mob.
        for (entity, pos, spawner) in spawner_data {
            let mut mob_list = spawner.mobs.clone();
            if mob_list.len() >= spawner.max_mob_count as usize {
                continue;
            }
            if now.duration_since(spawner.last_spawn) >= spawner.spawn_rate {
                let spawn_count = get_random_number_in_range(0, min(spawner.max_spawn_count_at_once, (spawner.max_mob_count - spawner.mobs.len() as u32)));
                for _ in 0..spawn_count {
                    let mob_entity = self.spawn_mob(spawner.mob_id, spawner.spawn_range, pos.clone());
                    
                    // Add the mob to the spawner's list of mobs.
                    mob_list.push(mob_entity);
                    
                    //TODO: Send a packet to all clients in the area to inform them of the new mob
                }
                
                // Update the spawner's last_spawn time in the world.
                let mut query = self.world.query_one::<(&Position, &mut Spawner)>(entity).unwrap();
                let (_pos, spawner_mut) = query.get().unwrap(); 
                spawner_mut.last_spawn = now;
                spawner_mut.mobs = mob_list;
            }
        }
    }
    
    pub fn get_nearby_objects(&self, id: Entity) -> Vec<Entity> {
        debug!("Getting nearby objects for entity {:?}", id);

        // Get the position of the query entity
        if let Ok(position) = self.world.get::<&Position>(id) {
            // Use spatial grid to find nearby entities
            let nearby_entities = self.spatial_grid.get_nearby_entities(
                self.world,
                Some(id), // Exclude the query entity itself
                &*position,
                position.map_id
            );

            debug!("Found {} nearby objects for entity {:?} at position ({}, {}, {}) on map {}",
                   nearby_entities.len(), id, position.x, position.y, position.z, position.map_id);

            nearby_entities
        } else {
            debug!("Entity {:?} has no position component", id);
            vec![]
        }
    }
    
    pub fn run(&mut self) {
        self.update_spawners();
        self.update_spatial_grid();
    }

    /// Updates the spatial grid with current entity positions
    /// This should be called periodically to keep the grid in sync
    fn update_spatial_grid(&mut self) {
        // For now, we rebuild the entire grid each time
        // In a more optimized implementation, we would track position changes
        // and only update entities that have moved
        self.spatial_grid.rebuild_from_world(self.world);
    }
}