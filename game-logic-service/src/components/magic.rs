
#[derive(Debug)]
struct Magic {
    mp: u32,
    max_mp: u32,
}

impl Default for Magic {
    fn default() -> Self {
        Self { mp: 55, max_mp: 55 }
    }
}

impl Magic {
    pub fn get_mp(&self) -> u32 {
        self.mp
    }

    pub fn get_max_mp(&self) -> u32 {
        self.max_mp
    }

    pub fn use_mp(&mut self, amount: u32) {
        self.mp = self.mp.saturating_sub(amount);
        if self.mp <= 0 {
            self.mp = 0;
        }
    }
    
    pub fn restore_mp(&mut self, amount: u32) {
        self.mp = self.mp.saturating_add(amount);
        if self.mp > self.max_mp {
            self.mp = self.max_mp;
        }
    }
}