use serde::Deserialize;

#[derive(Debug, <PERSON><PERSON>, Deserialize)]
pub struct Position {
    #[serde(rename = "X")]
    pub x: f32,
    #[serde(rename = "Y")]
    pub y: f32,
    #[serde(rename = "Z")]
    pub z: f32,
    pub map_id: u16,
    pub spawn_id: u16,
}

impl Default for Position {
    fn default() -> Self {
        // Set the default position to (520000.0, 520000.0)
        Self { x: 520000.0, y: 520000.0, z: 1.0, map_id: 20, spawn_id: 1 }
    }
}