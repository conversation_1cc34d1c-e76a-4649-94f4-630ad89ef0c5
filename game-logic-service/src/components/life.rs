
#[derive(Debug)]
pub(crate) struct Life {
    hp: u32,
    max_hp: u32,
}

impl Default for Life {
    fn default() -> Self {
        Self { hp: 100, max_hp: 100 }
    }
}

impl Life {
    pub fn new(hp: u32, max_hp: u32) -> Self {
        Self { hp, max_hp }
    }

    pub fn get_hp(&self) -> u32 {
        self.hp
    }

    pub fn get_max_hp(&self) -> u32 {
        self.max_hp
    }
    
    pub fn take_damage(&mut self, damage: u32) {
        self.hp = self.hp.saturating_sub(damage);
        if self.hp <= 0 {
            self.hp = 0;
        }
    }

    pub fn heal(&mut self, amount: u32) {
        self.hp = self.hp.saturating_add(amount);
        if self.hp > self.max_hp {
            self.hp = self.max_hp;
        }
    }
}