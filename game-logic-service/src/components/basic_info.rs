
#[derive(Debug)]
pub struct BasicInfo {
    pub name: String,
    pub id: u16,
    pub tag: u32,
    pub team_id: u32,
    pub job: u16,
    pub stat_points: u32,
    pub skill_points: u32,
    pub pk_flag: u16,
    pub stone: u8,
    pub char_id: u32,
}

impl Default for BasicInfo {
    fn default() -> Self {
        Self { name: "TEST".to_string(), id: 0, tag: 0, team_id: 0, job: 0, stat_points: 0, skill_points: 0, pk_flag: 0, stone: 0, char_id: 0 }
    }
}