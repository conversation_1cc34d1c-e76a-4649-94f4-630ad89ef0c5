#[derive(Debug)]
pub struct Stats {
    strength: u16,
    dexterity: u16,
    intelligence: u16,
    constitution: u16,
    charisma: u16,
    sense: u16,
    head_size: u8,
    body_size: u8,
}

impl Default for Stats {
    fn default() -> Self {
        Self { strength: 10, dexterity: 10, intelligence: 10, constitution: 10, charisma: 10, sense: 10, head_size: 1, body_size: 1 }
    }
}

impl Stats {
    pub fn get_strength(&self) -> u16 {
        self.strength
    }

    pub fn get_dexterity(&self) -> u16 {
        self.dexterity
    }

    pub fn get_intelligence(&self) -> u16 {
        self.intelligence
    }

    pub fn get_constitution(&self) -> u16 {
        self.constitution
    }

    pub fn get_charisma(&self) -> u16 {
        self.charisma
    }

    pub fn get_sense(&self) -> u16 {
        self.sense
    }

    pub fn get_head_size(&self) -> u8 {
        self.head_size
    }

    pub fn get_body_size(&self) -> u8 {
        self.body_size
    }
}
