
#[derive(Debug)]
struct CharacterGraphics {
    race: u8,
    hair: u8,
    face: u8,
}

impl Default for CharacterGraphics {
    fn default() -> Self {
        Self { race: 0, hair: 0, face: 0 }
    }
}

impl CharacterGraphics {
    pub fn new(race: u8, hair: u8, face: u8) -> Self {
        Self { race, hair, face }
    }

    pub fn get_race(&self) -> u8 {
        self.race
    }

    pub fn get_hair(&self) -> u8 {
        self.hair
    }

    pub fn get_face(&self) -> u8 {
        self.face
    }
}