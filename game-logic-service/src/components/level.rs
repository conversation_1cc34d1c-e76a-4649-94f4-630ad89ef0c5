#[derive(Debug)]
pub(crate) struct Level {
    pub level: u16,
    pub xp: u64,
    pub penalty_xp: u64,
}

impl Default for Level {
    fn default() -> Self {
        Self { level: 1, xp: 0, penalty_xp: 0 }
    }
}

impl Level {
    pub fn get_level(&self) -> u16 {
        self.level
    }

    pub fn get_xp(&self) -> u64 {
        self.xp
    }

    pub fn get_penalty_xp(&self) -> u64 {
        self.penalty_xp
    }

    pub fn add_xp(&mut self, amount: u64) {
        self.xp += amount;
    }

    pub fn add_penalty_xp(&mut self, amount: u64) {
        self.penalty_xp += amount;
    }
}
