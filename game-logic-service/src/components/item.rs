#[derive(Debug)]
pub struct Item {
    pub is_created: bool,
    pub is_zuly: bool,
    pub life: f32,
    pub durability: u8,
    pub has_socket: bool,
    pub is_appraised: bool,
    pub grade: u8,
    pub count: u32,
    pub gem_option: u16,
    pub price: u32,
}

impl Default for Item {
    fn default() -> Self {
        Self {
            is_created: false,
            is_zuly: false,
            life: 0.0,
            durability: 0,
            has_socket: false,
            is_appraised: false,
            grade: 0,
            count: 0,
            gem_option: 0,
            price: 0,
        }
    }
}