use std::time::{Duration, Instant};
use hecs::Entity;

#[derive(Debug)]
pub struct Spawner {
    /// The ID of the mob to be spawned.
    pub mob_id: u32,
    /// The maximum number of mobs that can be spawned.
    pub max_mob_count: u32,
    /// The maximum number of mobs that can be spawned at once.
    pub max_spawn_count_at_once: u32,
    /// The range within which the spawner should generate a mob.
    pub spawn_range: u32,
    /// The frequency in seconds at which the spawner should generate a mob.
    pub spawn_rate: Duration,
    /// The last time a mob was spawned.
    pub last_spawn: Instant,
    /// The list of mobs that have been spawned by this spawner.
    pub mobs: Vec<Entity>,
}

impl Default for Spawner {
    fn default() -> Self {
        Self {
            mob_id: 0,
            max_mob_count: 1,
            max_spawn_count_at_once: 1,
            spawn_rate: Duration::from_secs(10),
            last_spawn: Instant::now(),
            spawn_range: 100,
            mobs: Vec::new(),
        }
    }
}

impl Spawner {
    pub fn get_mob_id(&self) -> u32 {
        self.mob_id
    }

    pub fn get_max_mob_count(&self) -> u32 {
        self.max_mob_count
    }

    pub fn get_max_spawn_count_at_once(&self) -> u32 {
        self.max_spawn_count_at_once
    }

    pub fn get_spawn_rate(&self) -> Duration {
        self.spawn_rate
    }

    pub fn get_last_spawn(&self) -> Instant {
        self.last_spawn
    }

    pub fn get_spawn_range(&self) -> u32 {
        self.spawn_range
    }
}