[package]
name = "game-logic-service"
version = "0.1.0"
edition = "2021"

[dependencies]
utils = { path = "../utils" }
dotenv = "0.15"
tokio = { version = "1.45.1", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
hecs = { version = "0.10.5", features = ["serde", "default", "std"] }
serde_json = "1.0.140"
tracing = "0.1.41"
tracing-subscriber = { version = "0.3.19", features = ["env-filter", "chrono"] }
rand = "0.8.5"
tonic = "0.12.3"
prost = "0.13.4"
futures = "0.3.31"
tokio-stream = "0.1.17"
warp = "0.3.7"

[build-dependencies]
tonic-build = "0.12.3"
