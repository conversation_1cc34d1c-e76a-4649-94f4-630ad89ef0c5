LOG_LEVEL=info
REDIS_URL=redis://:super_safe_password@127.0.0.1:6379/0
LISTEN_ADDR=0.0.0.0
CONSUL_URL=http://consul:8500
POSTGRES_USER=osirose
POSTGRES_PASSWORD=super_safe_postgres_password
POSTGRES_DB=osirose

# Define service addresses for consul.
# This will set the service address in consul to what ever is placed here.
# This can be a IP address or the container name in docker
AUTH_SERVICE_ADDR=auth-service
API_SERVICE_ADDR=api-service
CHARACTER_SERVICE_ADDR=character-service
DATABASE_SERVICE_ADDR=database-service
PACKET_SERVICE_ADDR=packet-service
WORLD_SERVICE_ADDR=world-service

#<Service Name>_SERVICE_PORT=<PORT> # Replace Service name with the service and port with the port you want to run it on. Ex. AUTH_SERVICE_PORT=30000
#JWT_SECRET=safe_jwt_secret # This is only used for auth-service and is required.
#HEALTH_CHECK_PORT=8080 # This will change the health check port for the services