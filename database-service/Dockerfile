FROM rust:alpine AS builder
LABEL authors="raven"

RUN apk add --no-cache musl-dev libressl-dev protobuf-dev

WORKDIR /usr/src/utils
COPY ./utils .

WORKDIR /usr/src/proto
COPY ./proto .

WORKDIR /usr/src/database-service
COPY ./database-service .

RUN cargo build --release

FROM alpine:3

RUN apk add --no-cache libssl3 libgcc

COPY --from=builder /usr/src/database-service/target/release/database-service /usr/local/bin/database-service

EXPOSE 50052

CMD ["database-service"]