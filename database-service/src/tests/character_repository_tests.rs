#[cfg(test)]
mod tests {
    use crate::characters::{Character, CharacterRepository};
    use sqlx::{Pool, Postgres};
    use sqlx::postgres::PgPoolOptions;
    use std::sync::Arc;
    use tokio::sync::Mutex;
    use utils::redis_cache::RedisCache;
    use serde_json::json;

    // Helper function to create a test database pool
    async fn create_test_pool() -> Pool<Postgres> {
        let database_url = std::env::var("TEST_DATABASE_URL")
            .unwrap_or_else(|_| "postgres://postgres:postgres@localhost:5432/test_db".to_string());
        
        PgPoolOptions::new()
            .max_connections(5)
            .connect(&database_url)
            .await
            .expect("Failed to create test database pool")
    }

    // Helper function to create a mock Redis cache
    fn create_mock_redis() -> Arc<Mutex<RedisCache>> {
        let redis_url = std::env::var("TEST_REDIS_URL")
            .unwrap_or_else(|_| "redis://localhost:6379".to_string());
        
        Arc::new(Mutex::new(RedisCache::new(&redis_url)))
    }

    // Helper function to create a test user in the database
    async fn create_test_user(pool: &Pool<Postgres>) -> i32 {
        let result = sqlx::query!(
            r#"
            INSERT INTO "user" (name, email, role, "createdAt", "updatedAt")
            VALUES ('test_char_user', '<EMAIL>', 'user', NOW(), NOW())
            RETURNING id
            "#
        )
        .fetch_one(pool)
        .await
        .expect("Failed to create test user");

        result.id
    }

    // Helper function to create a test character in the database
    async fn create_test_character(pool: &Pool<Postgres>, user_id: i32, name: &str) -> i32 {
        let inventory = json!({
            "items": [],
            "capacity": 100
        });
        
        let stats = json!({
            "strength": 10,
            "dexterity": 10,
            "intelligence": 10,
            "vitality": 10
        });
        
        let skills = json!({
            "skills": []
        });
        
        let looks = json!({
            "race": 1,
            "gender": 0,
            "hair": 1,
            "face": 1
        });
        
        let position = json!({
            "mapId": 1,
            "x": 100.0,
            "y": 100.0,
            "z": 0.0
        });

        let result = sqlx::query!(
            r#"
            INSERT INTO character (
                "userId", name, money, inventory, stats, skills, looks, position, 
                "createdAt", "updatedAt", "isActive"
            )
            VALUES ($1, $2, 0, $3, $4, $5, $6, $7, NOW(), NOW(), true)
            RETURNING id
            "#,
            user_id,
            name,
            inventory,
            stats,
            skills,
            looks,
            position
        )
        .fetch_one(pool)
        .await
        .expect("Failed to create test character");

        result.id
    }

    // Helper function to clean up test data
    async fn cleanup_test_data(pool: &Pool<Postgres>, user_id: i32, character_id: i32) {
        sqlx::query!(r#"DELETE FROM character WHERE id = $1"#, character_id)
            .execute(pool)
            .await
            .expect("Failed to delete test character");
            
        sqlx::query!(r#"DELETE FROM "user" WHERE id = $1"#, user_id)
            .execute(pool)
            .await
            .expect("Failed to delete test user");
    }

    #[tokio::test]
    async fn test_get_character_by_id() {
        // Setup
        let pool = create_test_pool().await;
        let cache = create_mock_redis();
        let repo = CharacterRepository::new(pool.clone(), cache);
        
        // Create test user and character
        let user_id = create_test_user(&pool).await;
        let character_id = create_test_character(&pool, user_id, "test_character").await;
        
        // Test
        let result = repo.get_character_by_id(character_id).await;
        
        // Assert
        assert!(result.is_ok());
        let character = result.unwrap();
        assert_eq!(character.id, character_id);
        assert_eq!(character.name, "test_character");
        assert_eq!(character.user_id, user_id.to_string());
        
        // Cleanup
        cleanup_test_data(&pool, user_id, character_id).await;
    }

    #[tokio::test]
    async fn test_get_character_list() {
        // Setup
        let pool = create_test_pool().await;
        let cache = create_mock_redis();
        let repo = CharacterRepository::new(pool.clone(), cache);
        
        // Create test user and characters
        let user_id = create_test_user(&pool).await;
        let character_id1 = create_test_character(&pool, user_id, "test_character1").await;
        let character_id2 = create_test_character(&pool, user_id, "test_character2").await;
        
        // Test
        let result = repo.get_character_list(user_id.to_string()).await;
        
        // Assert
        assert!(result.is_ok());
        let characters = result.unwrap();
        assert_eq!(characters.len(), 2);
        
        // Cleanup
        cleanup_test_data(&pool, user_id, character_id1).await;
        sqlx::query!(r#"DELETE FROM character WHERE id = $1"#, character_id2)
            .execute(&pool)
            .await
            .expect("Failed to delete second test character");
    }

    #[tokio::test]
    async fn test_create_character() {
        // Setup
        let pool = create_test_pool().await;
        let cache = create_mock_redis();
        let repo = CharacterRepository::new(pool.clone(), cache);
        
        // Create test user
        let user_id = create_test_user(&pool).await;
        
        // Test
        let inventory = json!({
            "items": [],
            "capacity": 100
        });
        
        let stats = json!({
            "strength": 10,
            "dexterity": 10,
            "intelligence": 10,
            "vitality": 10
        });
        
        let skills = json!({
            "skills": []
        });
        
        let looks = json!({
            "race": 1,
            "gender": 0,
            "hair": 1,
            "face": 1
        });
        
        let position = json!({
            "mapId": 1,
            "x": 100.0,
            "y": 100.0,
            "z": 0.0
        });
        
        let result = repo.create_character(
            user_id.to_string(),
            "created_character",
            inventory,
            skills,
            stats,
            looks,
            position
        ).await;
        
        // Assert
        assert!(result.is_ok());
        let character_id = result.unwrap();
        
        // Verify character was created
        let character = repo.get_character_by_id(character_id).await.unwrap();
        assert_eq!(character.name, "created_character");
        
        // Cleanup
        cleanup_test_data(&pool, user_id, character_id).await;
    }

    #[tokio::test]
    async fn test_delete_character() {
        // Setup
        let pool = create_test_pool().await;
        let cache = create_mock_redis();
        let repo = CharacterRepository::new(pool.clone(), cache);
        
        // Create test user and character
        let user_id = create_test_user(&pool).await;
        let character_id = create_test_character(&pool, user_id, "delete_test_character").await;
        
        // Test
        let result = repo.delete_character(character_id, 1).await;
        
        // Assert
        assert!(result.is_ok());
        
        // Verify character was marked for deletion
        let character = repo.get_character_by_id(character_id).await.unwrap();
        assert!(character.deleted_at.is_some());
        
        // Cleanup
        cleanup_test_data(&pool, user_id, character_id).await;
    }

    #[tokio::test]
    async fn test_get_nonexistent_character() {
        // Setup
        let pool = create_test_pool().await;
        let cache = create_mock_redis();
        let repo = CharacterRepository::new(pool.clone(), cache);
        
        // Test
        let result = repo.get_character_by_id(99999).await;
        
        // Assert
        assert!(result.is_err());
    }

    #[tokio::test]
    async fn test_character_cache() {
        // Setup
        let pool = create_test_pool().await;
        let cache = create_mock_redis();
        let repo = CharacterRepository::new(pool.clone(), cache.clone());
        
        // Create test user and character
        let user_id = create_test_user(&pool).await;
        let character_id = create_test_character(&pool, user_id, "cache_test_character").await;
        
        // First call to populate cache
        let _ = repo.get_character_by_id(character_id).await.unwrap();
        
        // Delete from database to ensure we're getting from cache
        sqlx::query!(r#"DELETE FROM character WHERE id = $1"#, character_id)
            .execute(&pool)
            .await
            .expect("Failed to delete test character");
        
        // Test - should still work because of cache
        let result = repo.get_character_by_id(character_id).await;
        
        // Assert
        assert!(result.is_ok());
        let character = result.unwrap();
        assert_eq!(character.id, character_id);
        assert_eq!(character.name, "cache_test_character");
        
        // Cleanup
        sqlx::query!(r#"DELETE FROM "user" WHERE id = $1"#, user_id)
            .execute(&pool)
            .await
            .expect("Failed to delete test user");
    }
}
