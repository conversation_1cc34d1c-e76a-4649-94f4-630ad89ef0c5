#[cfg(test)]
mod tests {
    use crate::users::{User, UserRepository};
    use chrono::NaiveDateTime;
    use sqlx::{Pool, Postgres};
    use sqlx::postgres::PgPoolOptions;
    use std::sync::Arc;
    use tokio::sync::Mutex;
    use utils::redis_cache::RedisCache;

    // Helper function to create a test database pool
    async fn create_test_pool() -> Pool<Postgres> {
        let database_url = std::env::var("TEST_DATABASE_URL")
            .unwrap_or_else(|_| "postgres://postgres:postgres@localhost:5432/test_db".to_string());
        
        PgPoolOptions::new()
            .max_connections(5)
            .connect(&database_url)
            .await
            .expect("Failed to create test database pool")
    }

    // Helper function to create a mock Redis cache
    fn create_mock_redis() -> Arc<Mutex<RedisCache>> {
        let redis_url = std::env::var("TEST_REDIS_URL")
            .unwrap_or_else(|_| "redis://localhost:6379".to_string());
        
        Arc::new(Mutex::new(RedisCache::new(&redis_url)))
    }

    // Helper function to create a test user in the database
    async fn create_test_user(pool: &Pool<Postgres>, name: &str, email: &str) -> i32 {
        let result = sqlx::query!(
            r#"
            INSERT INTO "user" (name, email, role, "createdAt", "updatedAt")
            VALUES ($1, $2, 'user', NOW(), NOW())
            RETURNING id
            "#,
            name,
            email
        )
        .fetch_one(pool)
        .await
        .expect("Failed to create test user");

        result.id
    }

    // Helper function to clean up test data
    async fn cleanup_test_user(pool: &Pool<Postgres>, user_id: i32) {
        sqlx::query!(r#"DELETE FROM "user" WHERE id = $1"#, user_id)
            .execute(pool)
            .await
            .expect("Failed to delete test user");
    }

    #[tokio::test]
    async fn test_get_user_by_id() {
        // Setup
        let pool = create_test_pool().await;
        let cache = create_mock_redis();
        let repo = UserRepository::new(pool.clone(), cache);
        
        // Create test user
        let user_id = create_test_user(&pool, "test_user", "<EMAIL>").await;
        
        // Test
        let result = repo.get_user_by_id(user_id).await;
        
        // Assert
        assert!(result.is_ok());
        let user = result.unwrap();
        assert_eq!(user.id, user_id);
        assert_eq!(user.name, "test_user");
        assert_eq!(user.email, "<EMAIL>");
        assert_eq!(user.role, "user");
        
        // Cleanup
        cleanup_test_user(&pool, user_id).await;
    }

    #[tokio::test]
    async fn test_get_user_by_username() {
        // Setup
        let pool = create_test_pool().await;
        let cache = create_mock_redis();
        let repo = UserRepository::new(pool.clone(), cache);
        
        // Create test user
        let user_id = create_test_user(&pool, "test_user_by_name", "<EMAIL>").await;
        
        // Test
        let result = repo.get_user_by_username("test_user_by_name").await;
        
        // Assert
        assert!(result.is_ok());
        let user = result.unwrap();
        assert_eq!(user.id, user_id);
        assert_eq!(user.name, "test_user_by_name");
        assert_eq!(user.email, "<EMAIL>");
        
        // Cleanup
        cleanup_test_user(&pool, user_id).await;
    }

    #[tokio::test]
    async fn test_get_user_by_email() {
        // Setup
        let pool = create_test_pool().await;
        let cache = create_mock_redis();
        let repo = UserRepository::new(pool.clone(), cache);
        
        // Create test user
        let user_id = create_test_user(&pool, "test_user_by_email", "<EMAIL>").await;
        
        // Test
        let result = repo.get_user_by_email("<EMAIL>").await;
        
        // Assert
        assert!(result.is_ok());
        let user = result.unwrap();
        assert_eq!(user.id, user_id);
        assert_eq!(user.name, "test_user_by_email");
        assert_eq!(user.email, "<EMAIL>");
        
        // Cleanup
        cleanup_test_user(&pool, user_id).await;
    }

    #[tokio::test]
    async fn test_get_nonexistent_user() {
        // Setup
        let pool = create_test_pool().await;
        let cache = create_mock_redis();
        let repo = UserRepository::new(pool.clone(), cache);
        
        // Test
        let result = repo.get_user_by_id(99999).await;
        
        // Assert
        assert!(result.is_err());
    }

    #[tokio::test]
    async fn test_cache_hit() {
        // Setup
        let pool = create_test_pool().await;
        let cache = create_mock_redis();
        let repo = UserRepository::new(pool.clone(), cache.clone());
        
        // Create test user
        let user_id = create_test_user(&pool, "cache_test_user", "<EMAIL>").await;
        
        // First call to populate cache
        let _ = repo.get_user_by_id(user_id).await.unwrap();
        
        // Delete from database to ensure we're getting from cache
        cleanup_test_user(&pool, user_id).await;
        
        // Test - should still work because of cache
        let result = repo.get_user_by_id(user_id).await;
        
        // Assert
        assert!(result.is_ok());
        let user = result.unwrap();
        assert_eq!(user.id, user_id);
        assert_eq!(user.name, "cache_test_user");
    }
}
