#[cfg(test)]
mod tests {
    use crate::db::Database;
    use crate::grpc::character_db_service_server::{CharacterDbService, CharacterDbServiceServer};
    use crate::grpc::database_service::MyDatabaseService;
    use crate::grpc::user_service_server::{UserService, UserServiceServer};
    use crate::grpc::session_service_server::{SessionService, SessionServiceServer};
    use crate::grpc::{
        Character, CharacterListRequest, CharacterRequest, CreateCharacterRequest,
        DeleteCharacterRequest, GetSessionRequest, GetUserByEmailRequest, GetUserByUsernameRequest,
        GetUserRequest, RefreshSessionRequest,
    };
    use sqlx::postgres::PgPoolOptions;
    use std::sync::Arc;
    use tokio::sync::Mutex;
    use tonic::{Request, Response, Status};
    use tonic::transport::{Channel, Server};
    use utils::redis_cache::RedisCache;
    use uuid::Uuid;

    // Helper function to create a test database pool
    async fn create_test_pool() -> sqlx::PgPool {
        let database_url = std::env::var("TEST_DATABASE_URL")
            .unwrap_or_else(|_| "postgres://postgres:postgres@localhost:5432/test_db".to_string());
        
        PgPoolOptions::new()
            .max_connections(5)
            .connect(&database_url)
            .await
            .expect("Failed to create test database pool")
    }

    // Helper function to create a mock Redis cache
    fn create_mock_redis() -> Arc<Mutex<RedisCache>> {
        let redis_url = std::env::var("TEST_REDIS_URL")
            .unwrap_or_else(|_| "redis://localhost:6379".to_string());
        
        Arc::new(Mutex::new(RedisCache::new(&redis_url)))
    }

    // Helper function to create a test user in the database
    async fn create_test_user(pool: &sqlx::PgPool) -> i32 {
        let result = sqlx::query!(
            r#"
            INSERT INTO "user" (name, email, role, "createdAt", "updatedAt")
            VALUES ('grpc_test_user', '<EMAIL>', 'user', NOW(), NOW())
            RETURNING id
            "#
        )
        .fetch_one(pool)
        .await
        .expect("Failed to create test user");

        result.id
    }

    // Helper function to create a test character in the database
    async fn create_test_character(pool: &sqlx::PgPool, user_id: i32) -> i32 {
        let result = sqlx::query!(
            r#"
            INSERT INTO character (
                "userId", name, money, inventory, stats, skills, looks, position, 
                "createdAt", "updatedAt", "isActive"
            )
            VALUES (
                $1, 'grpc_test_character', 0, 
                '{"items":[]}'::jsonb, 
                '{"stats":{}}'::jsonb, 
                '{"skills":[]}'::jsonb, 
                '{"looks":{}}'::jsonb, 
                '{"position":{}}'::jsonb, 
                NOW(), NOW(), true
            )
            RETURNING id
            "#,
            user_id
        )
        .fetch_one(pool)
        .await
        .expect("Failed to create test character");

        result.id
    }

    // Helper function to create a test session in the database
    async fn create_test_session(pool: &sqlx::PgPool, user_id: i32) -> String {
        let session_id = Uuid::new_v4().to_string();
        
        sqlx::query!(
            r#"
            INSERT INTO session (id, "userId", "createdAt", "expiresAt")
            VALUES ($1, $2, NOW(), NOW() + INTERVAL '1 hour')
            "#,
            session_id,
            user_id
        )
        .execute(pool)
        .await
        .expect("Failed to create test session");

        session_id
    }

    // Helper function to clean up test data
    async fn cleanup_test_data(pool: &sqlx::PgPool, user_id: i32, character_id: i32, session_id: &str) {
        sqlx::query!(r#"DELETE FROM session WHERE id = $1"#, session_id)
            .execute(pool)
            .await
            .expect("Failed to delete test session");
            
        sqlx::query!(r#"DELETE FROM character WHERE id = $1"#, character_id)
            .execute(pool)
            .await
            .expect("Failed to delete test character");
            
        sqlx::query!(r#"DELETE FROM "user" WHERE id = $1"#, user_id)
            .execute(pool)
            .await
            .expect("Failed to delete test user");
    }

    // Helper function to start a test gRPC server
    async fn start_test_server() -> (
        String,
        sqlx::PgPool,
        i32,
        i32,
        String,
    ) {
        // Create test database pool and Redis cache
        let pool = create_test_pool().await;
        let redis_cache = create_mock_redis();
        
        // Create test data
        let user_id = create_test_user(&pool).await;
        let character_id = create_test_character(&pool, user_id).await;
        let session_id = create_test_session(&pool, user_id).await;
        
        // Create database service
        let db = Arc::new(Database::new(pool.clone(), redis_cache));
        let service = MyDatabaseService { db };
        
        // Start gRPC server
        let addr = "[::1]:0".parse().unwrap();
        let user_service = UserServiceServer::new(service.clone());
        let character_service = CharacterDbServiceServer::new(service.clone());
        let session_service = SessionServiceServer::new(service);
        
        let server = Server::builder()
            .add_service(user_service)
            .add_service(character_service)
            .add_service(session_service)
            .serve(addr);
        
        let server_addr = server.local_addr();
        tokio::spawn(server);
        
        (
            format!("http://{}", server_addr),
            pool,
            user_id,
            character_id,
            session_id,
        )
    }

    #[tokio::test]
    async fn test_user_service() {
        // Start test server
        let (server_addr, pool, user_id, character_id, session_id) = start_test_server().await;
        
        // Create client
        let channel = Channel::from_shared(server_addr.clone())
            .unwrap()
            .connect()
            .await
            .unwrap();
        
        let mut client = crate::grpc::user_service_client::UserServiceClient::new(channel);
        
        // Test GetUser
        let request = Request::new(GetUserRequest { user_id });
        let response = client.get_user(request).await.unwrap();
        let user = response.into_inner();
        
        assert_eq!(user.user_id, user_id);
        assert_eq!(user.username, "grpc_test_user");
        assert_eq!(user.email, "<EMAIL>");
        
        // Test GetUserByUsername
        let request = Request::new(GetUserByUsernameRequest {
            username: "grpc_test_user".to_string(),
        });
        let response = client.get_user_by_username(request).await.unwrap();
        let user = response.into_inner();
        
        assert_eq!(user.user_id, user_id);
        
        // Test GetUserByEmail
        let request = Request::new(GetUserByEmailRequest {
            email: "<EMAIL>".to_string(),
        });
        let response = client.get_user_by_email(request).await.unwrap();
        let user = response.into_inner();
        
        assert_eq!(user.user_id, user_id);
        
        // Cleanup
        cleanup_test_data(&pool, user_id, character_id, &session_id).await;
    }

    #[tokio::test]
    async fn test_character_service() {
        // Start test server
        let (server_addr, pool, user_id, character_id, session_id) = start_test_server().await;
        
        // Create client
        let channel = Channel::from_shared(server_addr.clone())
            .unwrap()
            .connect()
            .await
            .unwrap();
        
        let mut client = crate::grpc::character_db_service_client::CharacterDbServiceClient::new(channel);
        
        // Test GetCharacter
        let request = Request::new(CharacterRequest {
            user_id: user_id.to_string(),
            character_id,
        });
        let response = client.get_character(request).await.unwrap();
        let character = response.into_inner();
        
        assert_eq!(character.id, character_id);
        assert_eq!(character.name, "grpc_test_character");
        
        // Test GetCharacterList
        let request = Request::new(CharacterListRequest {
            user_id: user_id.to_string(),
        });
        let response = client.get_character_list(request).await.unwrap();
        let character_list = response.into_inner();
        
        assert_eq!(character_list.characters.len(), 1);
        assert_eq!(character_list.characters[0].id, character_id);
        
        // Cleanup
        cleanup_test_data(&pool, user_id, character_id, &session_id).await;
    }

    #[tokio::test]
    async fn test_session_service() {
        // Start test server
        let (server_addr, pool, user_id, character_id, session_id) = start_test_server().await;
        
        // Create client
        let channel = Channel::from_shared(server_addr.clone())
            .unwrap()
            .connect()
            .await
            .unwrap();
        
        let mut client = crate::grpc::session_service_client::SessionServiceClient::new(channel);
        
        // Test GetSession
        let request = Request::new(GetSessionRequest {
            session_id: session_id.clone(),
        });
        let response = client.get_session(request).await.unwrap();
        let session = response.into_inner();
        
        assert_eq!(session.session_id, session_id);
        assert_eq!(session.user_id, user_id.to_string());
        
        // Test RefreshSession
        let request = Request::new(RefreshSessionRequest {
            session_id: session_id.clone(),
        });
        let response = client.refresh_session(request).await.unwrap();
        let session = response.into_inner();
        
        assert_eq!(session.session_id, session_id);
        assert_eq!(session.user_id, user_id.to_string());
        
        // Cleanup
        cleanup_test_data(&pool, user_id, character_id, &session_id).await;
    }
}
