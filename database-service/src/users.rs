use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use std::sync::Arc;
use tokio::sync::Mutex;
use utils::redis_cache::{Cache, RedisCache};

#[derive(Debug, FromRow, Serialize, Deserialize)]
pub struct User {
    pub id: i32,
    pub name: String,
    pub email: String,
    pub role: String,
    pub created_at: chrono::NaiveDateTime,
    pub updated_at: chrono::NaiveDateTime,
}

pub struct UserRepository {
    pool: sqlx::PgPool,
    cache: Arc<Mutex<RedisCache>>, // Thread-safe RedisCache
}

impl UserRepository {
    pub fn new(pool: sqlx::PgPool, cache: Arc<Mutex<RedisCache>>) -> Self {
        Self { pool, cache }
    }

    pub async fn get_user_by_id(&self, user_id: i32) -> Result<User, sqlx::Error> {
        let cache_key = format!("user:{}", user_id);

        if let Some(user) = self
            .cache
            .lock()
            .await
            .get::<User>(&cache_key)
            .await
            .map_err(|_| sqlx::Error::RowNotFound)?
        {
            return Ok(user);
        }

        let user =
            sqlx::query_as::<_, User>("SELECT id, name, email, role, createdAt, updatedAt FROM user WHERE id = $1")
                .bind(user_id)
                .fetch_one(&self.pool)
                .await?;

        self.cache
            .lock()
            .await
            .set(&cache_key, &user, 300)
            .await
            .map_err(|_| sqlx::Error::RowNotFound)?;
        Ok(user)
    }

    pub async fn get_user_by_username(&self, username: &str) -> Result<User, sqlx::Error> {
        let cache_key = format!("user:username:{}", username);

        if let Some(user) = self
            .cache
            .lock()
            .await
            .get::<User>(&cache_key)
            .await
            .map_err(|_| sqlx::Error::RowNotFound)?
        {
            return Ok(user);
        }

        let user =
            sqlx::query_as::<_, User>("SELECT id, name, email, role, createdAt, updatedAt FROM user WHERE name = $1")
                .bind(username)
                .fetch_one(&self.pool)
                .await?;

        self.cache
            .lock()
            .await
            .set(&cache_key, &user, 300)
            .await
            .map_err(|_| sqlx::Error::RowNotFound)?;
        Ok(user)
    }

    pub async fn get_user_by_email(&self, email: &str) -> Result<User, sqlx::Error> {
        let cache_key = format!("user:email:{}", email);

        if let Some(user) = self
            .cache
            .lock()
            .await
            .get::<User>(&cache_key)
            .await
            .map_err(|_| sqlx::Error::RowNotFound)?
        {
            return Ok(user);
        }

        let user =
            sqlx::query_as::<_, User>("SELECT id, name, email, role, createdAt, updatedAt FROM user WHERE email = $1")
                .bind(email)
                .fetch_one(&self.pool)
                .await?;

        self.cache
            .lock()
            .await
            .set(&cache_key, &user, 300)
            .await
            .map_err(|_| sqlx::Error::RowNotFound)?;
        Ok(user)
    }

    pub async fn get_user_by_session(&self, session: &str) -> Result<User, sqlx::Error> {
        let cache_key = format!("user:session:{}", session);

        if let Some(user) = self
            .cache
            .lock()
            .await
            .get::<User>(&cache_key)
            .await
            .map_err(|_| sqlx::Error::RowNotFound)?
        {
            return Ok(user);
        }

        let user =
            sqlx::query_as::<_, User>("SELECT id, name, email, role, createdAt, updatedAt FROM user WHERE email = $1")
                .bind(session)
                .fetch_one(&self.pool)
                .await?;

        self.cache
            .lock()
            .await
            .set(&cache_key, &user, 300)
            .await
            .map_err(|_| sqlx::Error::RowNotFound)?;
        Ok(user)
    }
}
