use serde::{Deserialize, Serialize};
use sqlx::{FromRow, Row};
use std::sync::Arc;
use tokio::sync::Mutex;
use log::debug;
use utils::redis_cache::{Cache, RedisCache};

#[derive(Debug, FromRow, Serialize, Deserialize)]
pub struct Session {
    pub id: String,
    pub user_id: String,
}

pub struct SessionRepository {
    pool: sqlx::PgPool,
    cache: Arc<Mutex<RedisCache>>,
}

impl SessionRepository {
    pub fn new(pool: sqlx::PgPool, cache: Arc<Mutex<RedisCache>>) -> Self {
        Self { pool, cache }
    }

    pub async fn get_session(&self, session_id: &str) -> Result<Session, sqlx::Error> {
        let cache_key = format!("session:{}", session_id);

        debug!("get_session: {:?}", session_id);

        if let Some(session) = self
            .cache
            .lock()
            .await
            .get::<Session>(&cache_key)
            .await
            .map_err(|_| sqlx::Error::RowNotFound)?
        {
            debug!("Found session in cache: {:?}", session);
            return Ok(session);
        }

        debug!("Session not found in cache, fetching from database");

        // Fetch from the database
        let session = sqlx::query_as::<_, Session>("SELECT id, \"userId\" as user_id FROM session WHERE id = $1")
            .bind(session_id)
            .fetch_one(&self.pool)
            .await?;

        debug!("session: {:?}", session);

        self.cache
            .lock()
            .await
            .set(&cache_key, &session, 300)
            .await
            .map_err(|_| sqlx::Error::RowNotFound)?;
        Ok(session)
    }

    pub async fn refresh_session(&self, session_id: &str) -> Result<Session, sqlx::Error> {
        let cache_key = format!("session:{}", session_id);

        if let Some(session) = self
            .cache
            .lock()
            .await
            .get::<Session>(&cache_key)
            .await
            .map_err(|_| sqlx::Error::RowNotFound)?
        {
            self.cache
                .lock()
                .await
                .refresh(&cache_key, 300)
                .await
                .map_err(|_| sqlx::Error::RowNotFound)?;
            return Ok(session);
        }

        // Check to make sure the session is still valid
        let session = sqlx::query_as::<_, Session>("SELECT id, \"userId\" as user_id FROM session WHERE id = $1")
            .bind(session_id)
            .fetch_one(&self.pool)
            .await?;

        self.cache
            .lock()
            .await
            .set(&cache_key, &session, 300)
            .await
            .map_err(|_| sqlx::Error::RowNotFound)?;
        Ok(session)
    }
}
