use crate::grpc::database_service::MyDatabaseService;
use crate::grpc::session_service_server::SessionService;
use crate::grpc::{GetSessionRequest, GetSessionResponse, RefreshSessionRequest, RefreshSessionResponse};
use tonic::{Request, Response, Status};
use tracing::debug;

#[tonic::async_trait]
impl SessionService for MyDatabaseService {
    async fn get_session(&self, request: Request<GetSessionRequest>) -> Result<Response<GetSessionResponse>, Status> {
        let req = request.into_inner();
        debug!("get_session: {:?}", req);

        let session = self
            .db
            .session_repo
            .get_session(&req.session_id)
            .await
            .map_err(|_| Status::not_found("Session not found"))?;

        debug!("session: {:?}", session);
        Ok(Response::new(GetSessionResponse {
            session_id: session.id,
            user_id: session.user_id,
        }))
    }

    async fn refresh_session(
        &self,
        request: Request<RefreshSessionRequest>,
    ) -> Result<Response<RefreshSessionResponse>, Status> {
        let req = request.into_inner();
        debug!("get_session: {:?}", req);

        let session = self
            .db
            .session_repo
            .refresh_session(&req.session_id)
            .await
            .map_err(|_| Status::not_found("Session not found"))?;

        let valid = true;

        debug!("session: {:?}", session);
        Ok(Response::new(RefreshSessionResponse { valid }))
    }
}
