use crate::grpc::database_service::MyDatabaseService;
use crate::grpc::user_service_server::UserService;
use crate::grpc::{GetUserByEmailRequest, GetUserByUsernameRequest, GetUserRequest, GetUserResponse};
use tonic::{Request, Response, Status};

#[tonic::async_trait]
impl UserService for MyDatabaseService {
    async fn get_user(&self, request: Request<GetUserRequest>) -> Result<Response<GetUserResponse>, Status> {
        let req = request.into_inner();

        let user = self
            .db
            .user_repo
            .get_user_by_id(req.user_id)
            .await
            .map_err(|_| Status::not_found("User not found"))?;

        Ok(Response::new(GetUserResponse {
            user_id: user.id,
            username: user.name,
            email: user.email,
            role: user.role,
        }))
    }

    async fn get_user_by_username(
        &self,
        request: Request<GetUserByUsernameRequest>,
    ) -> Result<Response<GetUserResponse>, Status> {
        let req = request.into_inner();

        let user = self
            .db
            .user_repo
            .get_user_by_username(&req.username)
            .await
            .map_err(|_| Status::not_found("User not found"))?;

        Ok(Response::new(GetUserResponse {
            user_id: user.id,
            username: user.name,
            email: user.email,
            role: user.role,
        }))
    }

    async fn get_user_by_email(
        &self,
        request: Request<GetUserByEmailRequest>,
    ) -> Result<Response<GetUserResponse>, Status> {
        let req = request.into_inner();

        let user = self
            .db
            .user_repo
            .get_user_by_email(&req.email)
            .await
            .map_err(|_| Status::not_found("User not found"))?;

        Ok(Response::new(GetUserResponse {
            user_id: user.id,
            username: user.name,
            email: user.email,
            role: user.role,
        }))
    }
}
