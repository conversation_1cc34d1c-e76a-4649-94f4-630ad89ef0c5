# Database Service API Documentation

This document provides detailed information about the gRPC API endpoints exposed by the Database Service.

## UserService

### GetUser

Retrieves a user by their ID.

**Request:**
```protobuf
message GetUserRequest {
  int32 user_id = 1;
}
```

**Response:**
```protobuf
message GetUserResponse {
  int32 user_id = 1;
  string username = 2;
  string email = 3;
  string role = 4;
}
```

**Error Codes:**
- `NOT_FOUND`: User with the specified ID does not exist

### GetUserByUsername

Retrieves a user by their username.

**Request:**
```protobuf
message GetUserByUsernameRequest {
  string username = 1;
}
```

**Response:**
```protobuf
message GetUserResponse {
  int32 user_id = 1;
  string username = 2;
  string email = 3;
  string role = 4;
}
```

**Error Codes:**
- `NOT_FOUND`: User with the specified username does not exist

### GetUserByEmail

Retrieves a user by their email address.

**Request:**
```protobuf
message GetUserByEmailRequest {
  string email = 1;
}
```

**Response:**
```protobuf
message GetUserResponse {
  int32 user_id = 1;
  string username = 2;
  string email = 3;
  string role = 4;
}
```

**Error Codes:**
- `NOT_FOUND`: User with the specified email does not exist

## CharacterDbService

### GetCharacter

Retrieves a character by ID.

**Request:**
```protobuf
message CharacterRequest {
  string user_id = 1;
  int32 character_id = 2;
}
```

**Response:**
```protobuf
message Character {
  int32 id = 1;
  string user_id = 2;
  string name = 3;
  int64 money = 4;
  string inventory = 6;
  string stats = 7;
  string skills = 8;
  string looks = 9;
  string position = 10;
  string created_at = 11;
  string updated_at = 12;
  string deleted_at = 13;
  bool is_active = 14;
}
```

**Error Codes:**
- `NOT_FOUND`: Character with the specified ID does not exist

### GetCharacterList

Retrieves all characters for a user.

**Request:**
```protobuf
message CharacterListRequest {
  string user_id = 1;
}
```

**Response:**
```protobuf
message CharacterListResponse {
  repeated Character characters = 1;
}
```

### CreateCharacter

Creates a new character.

**Request:**
```protobuf
message CreateCharacterRequest {
  string user_id = 1;
  string name = 2;
  string inventory = 3; // JSON serialized
  string skills = 4;    // JSON serialized
  string stats = 5;     // JSON serialized
  string looks = 6;     // JSON serialized
  string position = 7;  // JSON serialized
}
```

**Response:**
```protobuf
message CreateCharacterResponse {
  int32 result = 1;
  int32 character_id = 2;
}
```

**Error Codes:**
- `INTERNAL`: Failed to create character

### DeleteCharacter

Marks a character for deletion.

**Request:**
```protobuf
message DeleteCharacterRequest {
  string user_id = 1;
  int32 character_id = 2;
  int32 delete_type = 3;
}
```

**Response:**
```protobuf
message DeleteCharacterResponse {
  int64 remaining_time = 1;
  string name = 2;
}
```

**Error Codes:**
- `INTERNAL`: Failed to delete character

## SessionService

### GetSession

Retrieves session information.

**Request:**
```protobuf
message GetSessionRequest {
  string session_id = 1;
}
```

**Response:**
```protobuf
message GetSessionResponse {
  string session_id = 1;
  string user_id = 2;
}
```

**Error Codes:**
- `NOT_FOUND`: Session with the specified ID does not exist

### RefreshSession

Updates session expiration.

**Request:**
```protobuf
message RefreshSessionRequest {
  string session_id = 1;
}
```

**Response:**
```protobuf
message RefreshSessionResponse {
  string session_id = 1;
  string user_id = 2;
}
```

**Error Codes:**
- `NOT_FOUND`: Session with the specified ID does not exist
