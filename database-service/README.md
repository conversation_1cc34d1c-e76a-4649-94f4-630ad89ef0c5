# Database Service

The Database Service is a core component of the MMORPG server architecture, providing centralized database access for user accounts, character data, and session management.

## Overview

The Database Service exposes gRPC endpoints that allow other services to interact with the PostgreSQL database and Redis cache. It implements the repository pattern to abstract database operations and provides efficient caching mechanisms to improve performance.

## Architecture

The service is built using the following components:

- **gRPC Server**: Exposes endpoints for user, character, and session operations
- **Repository Layer**: Implements data access logic with caching
- **PostgreSQL Database**: Stores persistent data
- **Redis Cache**: Provides high-speed caching for frequently accessed data

## Services

The Database Service exposes the following gRPC services:

### UserService

Handles user account operations:

- `GetUser`: Retrieves a user by ID
- `GetUserByUsername`: Retrieves a user by username
- `GetUserByEmail`: Retrieves a user by email

### CharacterDbService

Handles character operations:

- `GetCharacter`: Retrieves a character by ID
- `GetCharacterList`: Retrieves all characters for a user
- `CreateCharacter`: Creates a new character
- `Delete<PERSON>haracter`: Marks a character for deletion

### SessionService

Handles session management:

- `GetSession`: Retrieves session information
- `RefreshSession`: Updates session expiration

## Repositories

The service implements the repository pattern with the following repositories:

### UserRepository

Manages user data with methods for retrieving users by ID, username, email, or session.

### CharacterRepository

Manages character data with methods for creating, retrieving, and deleting characters.

### SessionRepository

Manages session data with methods for retrieving and refreshing sessions.

## Caching Strategy

The service uses Redis for caching with the following strategy:

- Cache keys follow a consistent pattern (e.g., `user:{id}`, `character:{id}`)
- Default TTL of 300 seconds (5 minutes)
- Cache invalidation on updates
- Fallback to database when cache misses

## Configuration

The service is configured using environment variables:

- `LISTEN_ADDR`: The address to listen on (default: 0.0.0.0)
- `SERVICE_PORT`: The port to listen on (default: 50052)
- `DATABASE_URL`: PostgreSQL connection string
- `REDIS_URL`: Redis connection string (default: redis://127.0.0.1:6379)

## Running the Service

```bash
# Set environment variables
export DATABASE_URL=postgres://username:password@localhost:5432/dbname
export REDIS_URL=redis://localhost:6379

# Run the service
cargo run
```

## Docker

The service can be run using Docker:

```bash
docker build -t database-service .
docker run -p 50052:50052 \
  -e DATABASE_URL=postgres://username:<EMAIL>:5432/dbname \
  -e REDIS_URL=redis://host.docker.internal:6379 \
  database-service
```

## Integration with External Systems

The Database Service is designed to work with an external database schema management system:

- **Schema Management**: The database schema is managed by an external web application using better-auth
- **User Management**: User creation and updates are handled by the external system
- **Session Management**: Initial session creation is handled by the external system

The Database Service primarily provides read access to this data for other microservices, with limited write capabilities for game-specific data like characters.
