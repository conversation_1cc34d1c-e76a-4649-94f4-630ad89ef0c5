syntax = "proto3";

package session_db_api;

service SessionService {
  rpc GetSession(GetSessionRequest) returns (GetSessionResponse);
  rpc RefreshSession(RefreshSessionRequest) returns (RefreshSessionResponse);
}

message GetSessionRequest {
  string session_id = 1;
}

message GetSessionResponse {
  string session_id = 1;
  string user_id = 2;
}

message RefreshSessionRequest {
  string session_id = 1;
}

message RefreshSessionResponse {
  bool valid = 1;
}