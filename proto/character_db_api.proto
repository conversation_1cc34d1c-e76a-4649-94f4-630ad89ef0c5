syntax = "proto3";

package character_db_api;

service CharacterDbService {
  rpc GetCharacter (CharacterRequest) returns (Character);
  rpc GetCharacterList (CharacterListRequest) returns (CharacterListResponse);
  rpc <PERSON>reate<PERSON>haracter (CreateCharacterRequest) returns (CreateCharacterResponse);
  rpc <PERSON>ete<PERSON>cter (DeleteCharacterRequest) returns (DeleteCharacterResponse);
}

message CharacterRequest {
  string user_id = 1;
  int32 character_id = 2;
}

message CharacterListRequest {
  string user_id = 1;
}

message CharacterListResponse {
  repeated Character characters = 1;
}

message CreateCharacterRequest {
  string user_id = 1;
  string name = 2;
  string inventory = 3; // JSON serialized
  string skills = 4;    // JSON serialized
  string stats = 5;     // JSON serialized
  string looks = 6;     // JSON serialized
  string position = 7;  // JSON serialized
}

message CreateCharacterResponse {
  int32 result = 1;
  int32 character_id = 2;
}

message DeleteCharacterRequest {
  string user_id = 1;
  int32 character_id = 2;
  int32 delete_type = 3;
}

message DeleteCharacterResponse {
  int64 remaining_time = 1;
  string name = 2;
}

message Character {
  int32 id = 1;
  string user_id = 2;
  string name = 3;
  int64 money = 4;
  string inventory = 6;
  string stats = 7;
  string skills = 8;
  string looks = 9;
  string position = 10;
  string created_at = 11;
  string updated_at = 12;
  string deleted_at = 13;
  bool is_active = 14;
}
