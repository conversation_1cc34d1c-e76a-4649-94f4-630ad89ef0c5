syntax = "proto3";

import "google/protobuf/empty.proto";
//google.protobuf.Empty
import "character_common.proto";

package game;

// Define a mob spawn event.
message MobSpawnEvent {
  uint32 id = 1;
  float pos_x = 2;
  float pos_y = 3;
  float dest_pos_x = 4;
  float dest_pos_y = 5;
  uint32 command = 6;
  uint32 target_id = 7;
  uint32 move_mode = 8;
  int32 hp = 9;
  int32 team_id = 10;
  uint32 status_flag = 11;
  uint32 npc_id = 12;
  uint32 quest_id = 13;
}

message NpcSpawnEvent {
  uint32 id = 1;
  float pos_x = 2;
  float pos_y = 3;
  float dest_pos_x = 4;
  float dest_pos_y = 5;
  uint32 command = 6;
  uint32 target_id = 7;
  uint32 move_mode = 8;
  int32 hp = 9;
  int32 team_id = 10;
  uint32 status_flag = 11;
  uint32 npc_id = 12;
  uint32 quest_id = 13;
  float angle = 14;
  uint32 event_status = 15;
}

message PlayerSpawnEvent {
  uint32 id = 1;
  float pos_x = 2;
  float pos_y = 3;
  float dest_pos_x = 4;
  float dest_pos_y = 5;
  uint32 command = 6;
  uint32 target_id = 7;
  uint32 move_mode = 8;
  int32 hp = 9;
  int32 team_id = 10;
  uint32 status_flag = 11;
  uint32 race = 12;
  uint32 run_speed = 13;
  uint32 atk_speed = 14;
  uint32 weight_rate = 15;
  uint32 face = 16;
  uint32 hair = 17;
  repeated character_common.EquippedItem inventory = 18;
  repeated character_common.ItemHeader bullets = 19;
  repeated character_common.EquippedItem riding_items = 20;
  uint32 job = 21;
  uint32 level = 22;
  uint32 z = 23;
  uint32 sub_flag = 24;
  string name = 25;
  string other_name = 26;
}

message ObjectDespawnEvent {
  uint32 object_id = 1;
}

// Define a player connect event.
message PlayerConnectEvent {
  string player_id = 1;
}

// Define a player disconnect event.
message PlayerDisconnectEvent {
  string player_id = 1;
}

message PlayerMoveEvent {
  string player_id = 1;
  float pos_x = 2;
  float pos_y = 3;
}

message PlayerAttackEvent {
  string player_id = 1;
  uint32 target_id = 2;
}

message PlayerSkillEvent {
  string player_id = 1;
  uint32 skill_id = 2;
  uint32 target_id = 3;
}

message MobAttackEvent {
  uint32 mob_id = 1;
  uint32 target_id = 2;
}

message MobSkillEvent {
  uint32 mob_id = 1;
  uint32 skill_id = 2;
  uint32 target_id = 3;
}

message DamageEvent {
  uint32 source_id = 1;
  uint32 target_id = 2;
  uint32 damage = 3;
  uint32 action = 4;
  float x = 5;
  float y = 6;
  character_common.Item item = 7;
  uint32 id = 8;
  uint32 owner_id = 9;
}

message DropItemEvent {
  float x = 1;
  float y = 2;
  character_common.Item item = 3;
  uint32 id = 4;
  uint32 owner_id = 5;
  uint32 time_to_live = 6;
}

message PlayerLevelUpEvent {
  string player_id = 1;
  uint32 level = 2;
  uint32 exp = 3;
  uint32 stat_points = 4;
  uint32 skill_points = 5;
}

// Define a generic event using oneof for the different types.
message GenericEvent {
  repeated string clients = 1; // List of clients to get this event.

  oneof event {
    MobSpawnEvent mob_spawn = 2;
    NpcSpawnEvent npc_spawn = 3;
    PlayerSpawnEvent player_spawn = 4;
    PlayerMoveEvent player_move = 5;
    PlayerAttackEvent player_attack = 6;
    PlayerSkillEvent player_skill = 7;
    MobAttackEvent mob_attack = 8;
    MobSkillEvent mob_skill = 9;
    DamageEvent damage = 10;
    ObjectDespawnEvent object_despawn = 11;
    DropItemEvent drop_item = 12;
    PlayerLevelUpEvent player_level_up = 13;
  }
}

// gRPC service definition for a streaming RPC.
service EventService {
  rpc StreamEvents(stream GenericEvent) returns (stream GenericEvent);
}