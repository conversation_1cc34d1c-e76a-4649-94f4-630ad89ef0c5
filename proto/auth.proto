syntax = "proto3";

package auth;

import "common.proto";

service AuthService {
  rpc Login(LoginRequest) returns (LoginResponse);
  rpc <PERSON>gout(LogoutRequest) returns (common.Empty);
  rpc ValidateToken(ValidateTokenRequest) returns (ValidateTokenResponse);
  rpc ValidateSession(ValidateSessionRequest) returns (ValidateSessionResponse);
  rpc RefreshSession(ValidateSessionRequest) returns (RefreshSessionResponse);
}

message LoginRequest {
  string username = 1;
  string password = 2;
  string ip_address = 3;
}

message LoginResponse {
  string token = 1;
  string user_id = 2;
  string session_id = 3;
}

message LogoutRequest {
  string session_id = 1;
}

message ValidateTokenRequest {
  string token = 1;
}

message ValidateTokenResponse {
  bool valid = 1;
  string user_id = 2;
  string session_id = 3;
}

message ValidateSessionRequest {
  string session_id = 1;
}

message ValidateSessionResponse {
  bool valid = 1;
  string session_id = 2;
  string user_id = 3;
}

message RefreshSessionResponse {
  bool valid = 1;
}


