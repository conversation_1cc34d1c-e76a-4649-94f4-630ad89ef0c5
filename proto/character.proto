syntax = "proto3";

package character;

import "character_common.proto";

service CharacterService {
    rpc GetCharacterList(GetCharacterListRequest) returns (GetCharacterListResponse);
    rpc CreateCharacter(CreateCharacterRequest) returns (CreateCharacterResponse);
    rpc DeleteCharacter(DeleteCharacterRequest) returns (DeleteCharacterResponse);
    rpc GetCharacter(GetCharacterRequest) returns (GetCharacterResponse);
}

message GetCharacterListRequest {
    string user_id = 1;
}

message GetCharacterListResponse {
    repeated character_common.Character characters = 1;
}

message CreateCharacterRequest {
    string user_id = 1;
    string name = 2;
    int32 race = 3;
    int32 face = 4;
    int32 hair = 5;
    int32 stone = 6;
}

message CreateCharacterResponse {
    int32 result = 1;
}

message DeleteCharacterRequest {
    string user_id = 1;
    string char_id = 2;
    int32 delete_type = 3;
}

message DeleteCharacterResponse {
    int64 remaining_time = 1;
    string name = 2;
}

message GetCharacterRequest {
    string user_id = 1;
    string char_id = 2;
}

message GetCharacterResponse {
    character_common.CharacterFull character = 1;
}
