syntax = "proto3";

package character_common;

message Stats {
  int32 level = 1;
  int32 job = 2;
  int32 str = 3;
  int32 dex = 4;
  int32 int = 5;
  int32 con = 6;
  int32 charm = 7;
  int32 sense = 8;
  int32 max_hp = 9;
  int32 max_mp = 10;
  int32 hp = 11;
  int32 mp = 12;
  int64 xp = 13;
  int32 head_size = 14;
  int32 body_size = 15;
  int32 stat_points = 16;
  int32 skill_points = 17;
  int32 penalty_xp = 18;
  int32 stamina = 19;
  int32 pat_hp = 20;
  int32 pat_cooldown_time = 21;
}

message Looks {
  int32 race = 1;
  int32 hair = 2;
  int32 face = 3;
  int32 stone = 4;
}

message EquippedItem {
  int32 item_id = 1;
  int32 gem_option = 2;
  int32 socket = 3;
  int32 grade = 4;
  int32 slot = 5;
}

message ItemHeader {
  int32 type_ = 1;
  int32 id = 2;
  int32 is_created = 3;
}

message Item {
  ItemHeader header = 1;
  int32 gem_option = 2;
  float durability = 3;
  float life = 4;
  int32 socket = 5;
  int32 is_appraised = 6;
  int32 grade = 7;
  int32 count = 8;
  int32 slot = 9;
}

message Location {
  int32 map_id = 1;
  float x = 2;
  float y = 3;
  int32 spawn_id = 4;
}

message Skill {
  int32 id = 1;
}

message Character {
  string character_id = 1; // Unique ID for the character
  string name = 2;         // Name of the character
  int64 last_played = 3;   // Last played timestamp (Unix time)
  int64 delete_time = 4;   // Time until character deletion (seconds)
  Stats stats = 5; // Character's stats
  Looks looks = 6; // Character's Looks
  repeated EquippedItem items = 7;
}

message CharacterFull {
  string character_id = 1;    // Unique ID for the character
  string name = 2;            // Name of the character
  int64 money = 3;
  Location position = 4;      // Character's position
  Looks looks = 5;            // Character's Looks
  Stats stats = 6;            // Character's stats
  repeated Skill skills = 7;  // Character's skills
  repeated Item items = 8;    // Character inventory
}