syntax = "proto3";

package chat;

import "common.proto";

service ChatService {
    rpc ChatStream(stream ChatMessage) returns (stream ChatMessage);
}

enum MessageType {
    MESSAGE_TYPE_UNSPECIFIED = 0;
    MESSAGE_TYPE_NORMAL = 1;
    MESSAGE_TYPE_SHOUT = 2;
    MESSAGE_TYPE_PARTY = 3;
    MESSAGE_TYPE_WHISPER = 4;
    MESSAGE_TYPE_CLAN = 5;
    MESSAGE_TYPE_ALLIED = 6;
}

message ChatMessage {
    string client_id = 1;
    MessageType type = 2;
    string message = 3;
    string target_id = 4;
    string sender = 5;
}
