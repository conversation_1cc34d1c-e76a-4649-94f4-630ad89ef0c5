syntax = "proto3";

package session_service_api;

import "common.proto";

service SessionService {
  rpc CreateSession (CreateSessionRequest) returns (SessionResponse);
  rpc GetSession (GetSessionRequest) returns (SessionResponse);
  rpc RefreshSession (GetSessionRequest) returns (RefreshSessionResponse);
  rpc DeleteSession (DeleteSessionRequest) returns (common.Empty);
}

message CreateSessionRequest {
  string session_id = 1;
  int32 user_id = 2;
  string username = 3;
  int32 character_id = 4;
  string ip_address = 5;
}

message GetSessionRequest {
  string session_id = 1;
}

message DeleteSessionRequest {
  string session_id = 1;
}

message SessionResponse {
  string session_id = 1;
  int32 user_id = 2;
  string username = 3;
  int32 character_id = 4;
  string login_time = 5;
  string ip_address = 6;
}

message RefreshSessionResponse {
  bool valid = 1;
}

