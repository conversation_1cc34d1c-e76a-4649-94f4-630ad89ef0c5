syntax = "proto3";

package user_db_api;

service UserService {
  rpc GetUser(GetUserRequest) returns (GetUserResponse);
  rpc GetUserByUsername(GetUserByUsernameRequest) returns (GetUserResponse);
  rpc GetUserByEmail(GetUserByEmailRequest) returns (GetUserResponse);
}

message GetUserRequest {
  int32 user_id = 1;
}

message GetUserByUsernameRequest {
  string username = 1;
}

message GetUserByEmailRequest {
  string email = 1;
}

message GetUserResponse {
  int32 user_id = 1;
  string username = 2;
  string email = 3;
  string role = 4;
}
