import subprocess
import os

# Define your images, tags, and Dockerfile paths
images = [
    "auth-service",
    "chat-service",
    "character-service",
    "database-service",
    "game-logic-service",
    "packet-service",
    "world-service"
]
dockerfile_paths = [
    "../auth-service/Dockerfile",
    "../chat-service/Dockerfile",
    "../character-service/Dockerfile",
    "../database-service/Dockerfile",
    "../game-logic-service/Dockerfile",
    "../packet-service/Dockerfile",
    "../world-service/Dockerfile",
]

common_tag = "latest"
version_tag = "v0.1.1"
image_tag_prefix = "gitea.azgstudio.com/raven/"
build_context = "../"

def run_command(command):
    """Run a shell command and handle errors."""
    try:
        subprocess.run(command, check=True)
    except subprocess.CalledProcessError as e:
        print(f"Error: Command '{' '.join(command)}' failed with exit code {e.returncode}")
        exit(1)


def build_images(images, dockerfile_paths, common_tag, version_tag, image_tag_prefix, build_context):
    """Build all Docker images."""
    for image, dockerfile_path in zip(images, dockerfile_paths):
        # Add the prefix to the image name
        full_image_name = f"{image_tag_prefix}{image}"

        # Build the image with both tags
        print(f"Building {full_image_name}:{version_tag} and {full_image_name}:{common_tag} using Dockerfile at {dockerfile_path}...")
        run_command([
            "docker", "build",
            "-t", f"{full_image_name}:{version_tag}",
            "-t", f"{full_image_name}:{common_tag}",
            "-f", dockerfile_path,
            build_context
        ])


def push_images(images, common_tag, version_tag, image_tag_prefix):
    """Push all Docker images."""
    for image in images:
        # Add the prefix to the image name
        full_image_name = f"{image_tag_prefix}{image}"

        # Push both tags
        print(f"Pushing {full_image_name}:{version_tag}...")
        run_command(["docker", "push", f"{full_image_name}:{version_tag}"])

        print(f"Pushing {full_image_name}:{common_tag}...")
        run_command(["docker", "push", f"{full_image_name}:{common_tag}"])


if __name__ == "__main__":
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)

    # Build all images first
    print("Starting the build phase...")
    build_images(images, dockerfile_paths, common_tag, version_tag, image_tag_prefix, build_context)

    # Push all images after builds are complete
    print("Starting the push phase...")
    push_images(images, common_tag, version_tag, image_tag_prefix)

