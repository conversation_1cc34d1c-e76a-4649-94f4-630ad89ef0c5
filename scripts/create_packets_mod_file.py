import os

# Prompt the user for the source directory
src_directory = input("Enter the path to the directory containing .rs files: ").strip()

# Validate the directory
if not os.path.isdir(src_directory):
    print(f"Error: '{src_directory}' is not a valid directory.")
    exit(1)

# Get a list of all .rs files in the directory (excluding `mod.rs` if it exists)
rs_files = [f for f in os.listdir(src_directory) if f.endswith(".rs") and f != "mod.rs"]

# Generate `pub mod` statements
mod_statements = []
for rs_file in rs_files:
    # Remove the .rs extension to get the module name
    mod_name = os.path.splitext(rs_file)[0]
    mod_statements.append(f"pub mod {mod_name};")

# Output the generated statements
output = "\n".join(mod_statements)
print("\nGenerated pub mod statements:\n")
print(output)

# Optionally write the output to mod.rs
mod_file_path = os.path.join(src_directory, "mod.rs")
with open(mod_file_path, "w") as mod_file:
    mod_file.write(output + "\n")

print(f"\nGenerated pub mod statements written to {mod_file_path}")
