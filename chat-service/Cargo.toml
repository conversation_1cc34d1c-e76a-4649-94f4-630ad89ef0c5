[package]
name = "chat-service"
version = "0.1.0"
edition = "2021"

[dependencies]
utils = { path = "../utils" }
dotenv = "0.15"
tokio = { version = "1.41.1", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
tracing = "0.1"
tracing-subscriber = { version = "0.3.19", features = ["env-filter", "chrono"] }
tonic = "0.12.3"
prost = "0.13.4"
warp = "0.3.7"
tonic-health = "0.12.3"
futures = "0.3.31"
uuid = "1.15.1"
tokio-stream = "0.1.17"

[build-dependencies]
tonic-build = "0.12.3"