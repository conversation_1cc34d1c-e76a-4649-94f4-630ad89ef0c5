FROM rust:alpine AS builder
LABEL authors="raven"

RUN apk add --no-cache musl-dev libressl-dev protobuf-dev

WORKDIR /usr/src/utils
COPY ./utils .

WORKDIR /usr/src/proto
COPY ./proto .

WORKDIR /usr/src/chat-service
COPY ./chat-service .

RUN cargo build --release

FROM alpine:3

RUN apk add --no-cache libssl3 libgcc

COPY --from=builder /usr/src/chat-service/target/release/chat-service /usr/local/bin/chat-service

EXPOSE 50054

CMD ["chat-service"]