use futures::{Stream, StreamExt};
use std::collections::HashMap;
use std::pin::Pin;
use std::sync::{Arc, Mutex};
use tonic::{Request, Response, Status};
use tonic::metadata::MetadataMap;
use tracing::debug;

pub mod common {
    tonic::include_proto!("common");
}
pub mod chat {
    tonic::include_proto!("chat");
}

use chat::chat_service_server::{ChatService, ChatServiceServer};
use chat::{ChatMessage, MessageType};

/// Type alias for storing client connections.
pub type Clients =
Arc<Mutex<HashMap<String, tokio::sync::mpsc::Sender<ChatMessage>>>>;

use crate::chat_channels::ChatChannel;

/// Our chat service struct.
#[derive(Clone)]
pub struct MyChatService {
    pub clients: Clients,
    pub local_channel: Arc<dyn ChatChannel>,
    pub shout_channel: Arc<dyn ChatChannel>,
    pub guild_channel: Arc<dyn ChatChannel>,
}

impl MyChatService {
    /// Wrap our service as a gRPC service.
    pub fn into_service(self) -> ChatServiceServer<Self> {
        ChatServiceServer::new(self)
    }
}

fn get_authenticated_id(metadata: &MetadataMap) -> Result<String, Status> {
    if let Some(client_id_val) = metadata.get("x-client-id") {
        // Convert the header to a string.
        client_id_val
            .to_str()
            .map(ToString::to_string)
            .map_err(|_| Status::unauthenticated("Invalid client ID header"))
    } else {
        Err(Status::unauthenticated("Missing client ID header"))
    }
}

#[tonic::async_trait]
impl ChatService for MyChatService {
    type ChatStreamStream =
    Pin<Box<dyn Stream<Item = Result<ChatMessage, Status>> + Send + Sync + 'static>>;

    async fn chat_stream(
        &self,
        request: Request<tonic::Streaming<ChatMessage>>,
    ) -> Result<Response<Self::ChatStreamStream>, Status> {
        debug!("New chat client connected");
        debug!("request: {:?}", request);

        let client_id = get_authenticated_id(request.metadata())?;
        let mut inbound = request.into_inner();

        // Create a channel for sending outbound messages to this client.
        let (tx, rx) = tokio::sync::mpsc::channel(32);

        {
            let mut clients = self.clients.lock().unwrap();
            clients.insert(client_id.clone(), tx);
        }

        // Clone shared resources for the spawned task.
        let clients_clone = self.clients.clone();
        let local_channel = self.local_channel.clone();
        let shout_channel = self.shout_channel.clone();
        let guild_channel = self.guild_channel.clone();

        tokio::spawn(async move {
            while let Some(result) = inbound.next().await {
                match result {
                    Ok(message) => {
                        debug!("message: {:?}", message);
                        // Dispatch based on the chat type.
                        match TryFrom::try_from(message.r#type)
                            .unwrap_or(MessageType::Normal)
                        {
                            MessageType::Normal => {
                                local_channel.handle_message(
                                    message,
                                    &client_id,
                                    &clients_clone,
                                );
                            }
                            MessageType::Shout => {
                                shout_channel.handle_message(
                                    message,
                                    &client_id,
                                    &clients_clone,
                                );
                            }
                            MessageType::Clan => {
                                guild_channel.handle_message(
                                    message,
                                    &client_id,
                                    &clients_clone,
                                );
                            }
                            // For other types, we simply broadcast to all as default.
                            _ => {
                                let clients_lock = clients_clone.lock().unwrap();
                                for (id, tx) in clients_lock.iter() {
                                    if id != &client_id {
                                        let _ = tx.try_send(message.clone());
                                    }
                                }
                            }
                        }
                    }
                    Err(e) => {
                        eprintln!("Error receiving message from {}: {:?}", client_id, e);
                        break;
                    }
                }
            }

            // Remove the client when the stream ends.
            let mut clients = clients_clone.lock().unwrap();
            clients.remove(&client_id);
            println!("Client {} disconnected", client_id);
        });

        // Convert the rx half into a stream for the response.
        let out_stream = tokio_stream::wrappers::ReceiverStream::new(rx)
            .map(|msg| Ok(msg));
        Ok(Response::new(Box::pin(out_stream) as Self::ChatStreamStream))
    }
}