use crate::chat_channels::ChatChannel;
use crate::chat_service::Clients;
use crate::chat_service::chat::ChatMessage;

/// A dedicated module for shout chat.
#[derive(Debug)]
pub struct ShoutChat;

impl ChatChannel for ShoutChat {
    fn handle_message(&self, message: ChatMessage, sender_id: &str, clients: &Clients) {
        // TODO: make sure the clients are on the same map
        let new_message = ChatMessage {
            client_id: sender_id.to_string(),
            r#type: message.r#type,
            message: message.message,
            target_id: message.target_id,
            sender: message.sender,
        };
        let clients_lock = clients.lock().unwrap();
        for (id, tx) in clients_lock.iter() {
            let _ = tx.try_send(new_message.clone());
        }
    }
}