use crate::chat_channels::ChatChannel;
use crate::chat_service::Clients;
use crate::chat_service::chat::ChatMessage;

/// A dedicated module for guild chat.
#[derive(Debug)]
pub struct GuildChat;

impl ChatChannel for GuildChat {
    fn handle_message(&self, message: ChatMessage, sender_id: &str, clients: &Clients) {
        //TODO: Make sure the clients actually are apart of the same guild.

        let new_message = ChatMessage {
            client_id: sender_id.to_string(),
            r#type: message.r#type,
            message: message.message,
            target_id: message.target_id,
            sender: message.sender,
        };
        
        // let clients_lock = clients.lock().unwrap();
        // for (id, tx) in clients_lock.iter() {
        //     if id != sender_id && id.contains("guild") {
        //         let _ = tx.try_send(new_message.clone());
        //     }
        // }
    }
}